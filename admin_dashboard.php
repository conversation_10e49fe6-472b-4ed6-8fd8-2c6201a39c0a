<?php
require_once 'db_connect.php';
require_admin();

// Fetch stats
$stmt = $pdo->query("SELECT COUNT(*) as total FROM users WHERE is_admin = 0");
$total_users = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

$stmt = $pdo->query("SELECT COUNT(*) as total FROM bookings");
$total_bookings = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

$stmt = $pdo->query("SELECT COUNT(*) as total FROM workspaces");
$total_workspaces = $stmt->fetch(PDO::FETCH_ASSOC)['total'];

// Handle logout
if (isset($_GET['logout'])) {
    session_destroy();
    header("Location: login.php");
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OfficeLink - Admin Dashboard</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">OfficeLink Admin</div>
            <nav>
                <div class="hamburger" onclick="toggleMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <ul id="nav-menu">
                    <li><a href="admin_dashboard.php">Dashboard</a></li>
                    <li><a href="admin_workspaces.php">Manage Workspaces</a></li>
                    <li><a href="admin_bookings.php">Manage Bookings</a></li>
                    <li><a href="admin_users.php">Manage Users</a></li>
                    <li><a href="?logout=true">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Admin Dashboard Section -->
    <section id="admin-dashboard" class="admin-section">
        <div class="container">
            <h2>Admin Dashboard</h2>
            <div class="stats-grid">
                <div class="stat-card">
                    <h3>Total Users</h3>
                    <p><?php echo $total_users; ?></p>
                </div>
                <div class="stat-card">
                    <h3>Total Bookings</h3>
                    <p><?php echo $total_bookings; ?></p>
                </div>
                <div class="stat-card">
                    <h3>Total Workspaces</h3>
                    <p><?php echo $total_workspaces; ?></p>
                </div>
            </div>
            <div class="admin-actions">
                <a href="admin_workspaces.php" class="btn-primary">Manage Workspaces</a>
                <a href="admin_bookings.php" class="btn-primary">Manage Bookings</a>
                <a href="admin_users.php" class="btn-primary">Manage Users</a>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>OfficeLink</h4>
                    <p>Flexible workspaces for everyone.</p>
                </div>
                <div class="footer-section">
                    <h4>Links</h4>
                    <ul>
                        <li><a href="index.php#features">Features</a></li>
                        <li><a href="index.php#about">About</a></li>
                        <li><a href="signup.php">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +251 123 456 789</p>
                </div>
            </div>
            <p class="footer-bottom"> <?php echo date("Y"); ?> OfficeLink. All rights reserved.</p>
        </div>
    </footer>

    <script>
        function toggleMenu() {
            const menu = document.getElementById('nav-menu');
            const hamburger = document.querySelector('.hamburger');
            menu.classList.toggle('active');
            hamburger.classList.toggle('active');
        }
    </script>
</body>
</html>