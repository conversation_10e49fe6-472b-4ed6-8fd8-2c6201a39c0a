<?php
require_once 'db_connect.php';
require_once 'chapa_config.php';

// This file handles Chapa webhook callbacks
// It should be called by <PERSON><PERSON> when payment status changes

// Get the raw POST data
$input = file_get_contents('php://input');
$data = json_decode($input, true);

// Log the callback
log_payment_event('callback_received', [
    'raw_data' => $input,
    'parsed_data' => $data
]);

// Verify the webhook signature (if <PERSON><PERSON> provides one)
$signature = $_SERVER['HTTP_X_CHAPA_SIGNATURE'] ?? '';

if ($signature) {
    $chapa = new ChapaPayment();
    if (!$chapa->validate_webhook($input, $signature)) {
        log_payment_event('callback_signature_invalid', ['signature' => $signature]);
        http_response_code(400);
        exit('Invalid signature');
    }
}

// Process the callback
if ($data && isset($data['tx_ref'])) {
    $tx_ref = $data['tx_ref'];
    $status = $data['status'] ?? '';
    $chapa_reference = $data['reference'] ?? '';
    
    try {
        // Find the payment record
        $stmt = $pdo->prepare("SELECT p.*, b.id as booking_id FROM payments p 
                              JOIN bookings b ON p.booking_id = b.id 
                              WHERE p.tx_ref = ?");
        $stmt->execute([$tx_ref]);
        $payment = $stmt->fetch();
        
        if ($payment) {
            // Update payment status
            $payment_status = '';
            $booking_status = '';
            $payment_status_db = '';
            
            switch (strtolower($status)) {
                case 'success':
                case 'successful':
                    $payment_status = PAYMENT_STATUS_SUCCESS;
                    $booking_status = 'confirmed';
                    $payment_status_db = 'paid';
                    break;
                case 'failed':
                case 'failure':
                    $payment_status = PAYMENT_STATUS_FAILED;
                    $booking_status = 'cancelled';
                    $payment_status_db = 'unpaid';
                    break;
                case 'cancelled':
                    $payment_status = PAYMENT_STATUS_CANCELLED;
                    $booking_status = 'cancelled';
                    $payment_status_db = 'unpaid';
                    break;
                default:
                    $payment_status = PAYMENT_STATUS_PENDING;
                    $booking_status = 'pending';
                    $payment_status_db = 'unpaid';
            }
            
            // Update payment record
            $stmt = $pdo->prepare("UPDATE payments SET 
                                  status = ?, 
                                  chapa_reference = ?, 
                                  chapa_response = ?, 
                                  updated_at = NOW() 
                                  WHERE tx_ref = ?");
            $stmt->execute([
                $payment_status,
                $chapa_reference,
                json_encode($data),
                $tx_ref
            ]);
            
            // Update booking status
            $stmt = $pdo->prepare("UPDATE bookings SET 
                                  status = ?, 
                                  payment_status = ?, 
                                  updated_at = NOW() 
                                  WHERE id = ?");
            $stmt->execute([
                $booking_status,
                $payment_status_db,
                $payment['booking_id']
            ]);
            
            // Log the payment update
            log_payment_event('payment_updated', [
                'tx_ref' => $tx_ref,
                'status' => $payment_status,
                'booking_id' => $payment['booking_id'],
                'chapa_reference' => $chapa_reference
            ]);
            
            // Send confirmation email (optional - implement if needed)
            if ($payment_status === PAYMENT_STATUS_SUCCESS) {
                // You can implement email notification here
                log_payment_event('payment_success', [
                    'tx_ref' => $tx_ref,
                    'booking_id' => $payment['booking_id'],
                    'amount' => $payment['amount']
                ]);
            }
            
            http_response_code(200);
            echo 'OK';
            
        } else {
            log_payment_event('callback_payment_not_found', ['tx_ref' => $tx_ref]);
            http_response_code(404);
            echo 'Payment not found';
        }
        
    } catch (PDOException $e) {
        log_payment_event('callback_database_error', [
            'error' => $e->getMessage(),
            'tx_ref' => $tx_ref
        ]);
        http_response_code(500);
        echo 'Database error';
    }
    
} else {
    log_payment_event('callback_invalid_data', ['data' => $data]);
    http_response_code(400);
    echo 'Invalid callback data';
}
?>
