-- OfficeLink Database Schema
-- Run this script to create the database and tables

CREATE DATABASE IF NOT EXISTS officelink;
USE officelink;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    full_name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password VARCHAR(255) NOT NULL,
    phone_number VARCHAR(20),
    is_admin TINYINT(1) DEFAULT 0,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Workspaces table
CREATE TABLE IF NOT EXISTS workspaces (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    location VARCHAR(255) NOT NULL,
    description TEXT,
    noise_level ENUM('quiet', 'moderate', 'lively') DEFAULT 'moderate',
    amenities TEXT,
    hourly_rate DECIMAL(10,2) NOT NULL,
    half_day_rate DECIMAL(10,2) NOT NULL,
    full_day_rate DECIMAL(10,2) NOT NULL,
    monthly_rate DECIMAL(10,2) NOT NULL,
    image_url VARCHAR(500),
    is_active TINYINT(1) DEFAULT 1,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Bookings table
CREATE TABLE IF NOT EXISTS bookings (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    workspace_id INT NOT NULL,
    workspace_name VARCHAR(255) NOT NULL,
    location VARCHAR(255) NOT NULL,
    booking_date DATE NOT NULL,
    booking_type ENUM('hourly', 'half-day', 'full-day', 'monthly') NOT NULL,
    start_time DATETIME,
    end_time DATETIME,
    total_cost DECIMAL(10,2) NOT NULL,
    status ENUM('pending', 'confirmed', 'cancelled', 'completed') DEFAULT 'pending',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE
);

-- Bookmarks table
CREATE TABLE IF NOT EXISTS bookmarks (
    id INT AUTO_INCREMENT PRIMARY KEY,
    user_id INT NOT NULL,
    workspace_id INT NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    FOREIGN KEY (workspace_id) REFERENCES workspaces(id) ON DELETE CASCADE,
    UNIQUE KEY unique_bookmark (user_id, workspace_id)
);

-- Insert default admin user (password: admin123)
INSERT IGNORE INTO users (full_name, email, password, is_admin) 
VALUES ('Admin User', '<EMAIL>', 'admin123', 1);

-- Add is_active column to existing workspaces table if it doesn't exist
ALTER TABLE workspaces ADD COLUMN IF NOT EXISTS is_active TINYINT(1) DEFAULT 1;

-- Sample workspaces (optional)
INSERT IGNORE INTO workspaces (name, location, description, noise_level, amenities, hourly_rate, half_day_rate, full_day_rate, monthly_rate, image_url) VALUES
('Modern Co-working Space', 'Downtown Business District', 'A modern co-working space with high-speed internet and comfortable seating.', 'moderate', 'WiFi, Coffee, Printing, Meeting Rooms', 15.00, 50.00, 80.00, 800.00, 'uploads/workspace-default.jpg'),
('Quiet Study Room', 'Library District', 'Perfect for focused work and study sessions.', 'quiet', 'WiFi, Silence Policy, Individual Desks', 12.00, 40.00, 65.00, 650.00, 'uploads/workspace-default.jpg'),
('Creative Hub', 'Arts Quarter', 'Vibrant space for creative professionals and teams.', 'lively', 'WiFi, Whiteboard, Projector, Kitchen', 18.00, 60.00, 95.00, 950.00, 'uploads/workspace-default.jpg');
