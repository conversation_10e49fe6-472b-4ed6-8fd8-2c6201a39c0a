<?php
require_once 'db_connect.php';

// Check if user is logged in
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;
$is_admin = isset($_SESSION['is_admin']) ? $_SESSION['is_admin'] : false;
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OfficeLink - Coworking & Meeting Room Reservations</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
<header>
    <div class="container">
        <div class="logo">OfficeLink</div>
        <nav>
            <div class="hamburger" onclick="toggleMenu()">
                <span></span>
                <span></span>
                <span></span>
            </div>
            <ul id="nav-menu">
                <li><a href="index.php">Home</a></li>
                <li><a href="index.php#features">Features</a></li>
                <li><a href="index.php#about">About</a></li>
                <li><a href="search.php">Search</a></li>
                <?php if ($user_id): ?>
                    <?php if ($is_admin): ?>
                        <li><a href="admin_dashboard.php">Admin Dashboard</a></li>
                    <?php else: ?>
                        <li><a href="dashboard.php">Dashboard</a></li>
                    <?php endif; ?>
                    <li><a href="dashboard.php?logout=1">Logout</a></li>
                <?php else: ?>
                    <li><a href="signup.php" class="btn-signup">Sign Up</a></li>
                    <li><a href="login.php">Login</a></li>
                <?php endif; ?>
            </ul>
        </nav>
    </div>
</header>

    <!-- Hero Section -->
    <section id="hero" class="hero">
        <div class="container">
            <div class="hero-content">
                <h1>Find Your Perfect Workspace</h1>
                <p>Book coworking spaces and meeting rooms in Addis Ababa and beyond with ease.</p>
                <a href="search.php" class="btn-primary">Get Started</a>
            </div>
            <div class="hero-image">
                <img src="images/coworking2.jpg" alt="Coworking Space">
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section id="features" class="features">
        <div class="container">
            <h2>Why Choose OfficeLink?</h2>
            <div class="feature-grid">
    <div class="feature-item">
        <img src="icons/location Icon/location.png" alt="Location Icon">
        <h3>Location-Based Search</h3>
        <p>Find spaces near you in Addis Ababa and other cities.</p>
    </div>
    <div class="feature-item">
        <img src="icons/clock icon/timetable1.png" alt="Clock Icon">
        <h3>Flexible Booking</h3>
        <p>Reserve hourly, daily, or monthly—your choice!</p>
    </div>
    <div class="feature-item">
        <img src="icons/price icon/online-shop.png" alt="Price Icon">
        <h3>Transparent Pricing</h3>
        <p>See the full cost upfront, no hidden fees.</p>
    </div>
</div>
        </div>
    </section>

    <!-- How It Works Section -->
    <section id="how-it-works" class="how-it-works">
        <div class="container">
            <h2>How It Works</h2>
            <div class="steps">
                <div class="step">
                    <span>1</span>
                    <h3>Search</h3>
                    <p>Find coworking spaces by location and amenities.</p>
                </div>
                <div class="step">
                    <span>2</span>
                    <h3>Book</h3>
                    <p>Choose your time slot and reserve instantly.</p>
                </div>
                <div class="step">
                    <span>3</span>
                    <h3>Work</h3>
                    <p>Enjoy a productive day in your perfect space.</p>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>OfficeLink</h4>
                    <p>Flexible workspaces for everyone.</p>
                </div>
                <div class="footer-section">
                    <h4>Links</h4>
                    <ul>
                        <li><a href="#features">Features</a></li>
                        <li><a href="#about">About</a></li>
                        <li><a href="signup.php">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +251 123 456 78</p>
                </div>
            </div>
            <p class="footer-bottom"> <?php echo date("Y"); ?> OfficeLink. All rights reserved.</p>
        </div>
    </footer>
    <script>
    function toggleMenu() {
        const menu = document.getElementById('nav-menu');
        const hamburger = document.querySelector('.hamburger');
        menu.classList.toggle('active');
        hamburger.classList.toggle('active');
    }
</script>
</body>
</html>