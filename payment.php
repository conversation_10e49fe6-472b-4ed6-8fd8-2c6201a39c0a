<?php
require_once 'db_connect.php';
require_once 'chapa_config.php';
require_login();

$payment_error = '';
$payment_success = '';

// Check if booking_id is provided
if (!isset($_GET['booking_id'])) {
    header("Location: dashboard.php");
    exit;
}

$booking_id = filter_var($_GET['booking_id'], FILTER_VALIDATE_INT);
if ($booking_id === false) {
    header("Location: dashboard.php");
    exit;
}

$user_id = $_SESSION['user_id'];

// Fetch booking details
try {
    $stmt = $pdo->prepare("SELECT b.*, u.full_name, u.email, u.phone_number 
                          FROM bookings b 
                          JOIN users u ON b.user_id = u.id 
                          WHERE b.id = ? AND b.user_id = ?");
    $stmt->execute([$booking_id, $user_id]);
    $booking = $stmt->fetch();

    if (!$booking) {
        header("Location: dashboard.php");
        exit;
    }

    // Check if booking is already paid
    if ($booking['payment_status'] === 'paid') {
        header("Location: dashboard.php?message=already_paid");
        exit;
    }

} catch (PDOException $e) {
    error_log("Payment page error: " . $e->getMessage());
    $payment_error = "Error loading booking details.";
}

// Handle payment initialization
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['pay_now'])) {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $payment_error = "Invalid request. Please try again.";
    } else {
        try {
            // Generate transaction reference
            $tx_ref = ChapaPayment::generate_tx_ref('OL_BOOKING');
            
            // Prepare payment data
            $payment_data = [
                'amount' => ChapaPayment::format_amount($booking['total_cost']),
                'currency' => CHAPA_CURRENCY,
                'email' => $booking['email'],
                'first_name' => explode(' ', $booking['full_name'])[0],
                'last_name' => explode(' ', $booking['full_name'], 2)[1] ?? '',
                'phone_number' => $booking['phone_number'] ?? '',
                'tx_ref' => $tx_ref,
                'callback_url' => get_site_url() . '/payment_callback.php',
                'return_url' => get_site_url() . '/payment_success.php',
                'description' => "OfficeLink Workspace Booking - {$booking['workspace_name']}",
                'meta' => [
                    'booking_id' => $booking_id,
                    'user_id' => $user_id,
                    'workspace_name' => $booking['workspace_name']
                ]
            ];

            // Initialize Chapa payment
            $chapa = new ChapaPayment();
            $response = $chapa->initialize_payment($payment_data);

            if ($response['success']) {
                // Save payment record
                $stmt = $pdo->prepare("INSERT INTO payments (booking_id, user_id, tx_ref, amount, currency, status, chapa_response) VALUES (?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([
                    $booking_id,
                    $user_id,
                    $tx_ref,
                    $booking['total_cost'],
                    CHAPA_CURRENCY,
                    PAYMENT_STATUS_PENDING,
                    json_encode($response['data'])
                ]);

                // Log payment initialization
                log_payment_event('payment_initialized', [
                    'booking_id' => $booking_id,
                    'tx_ref' => $tx_ref,
                    'amount' => $booking['total_cost']
                ]);

                // Redirect to Chapa checkout
                $checkout_url = $response['data']['data']['checkout_url'];
                header("Location: $checkout_url");
                exit;

            } else {
                $payment_error = "Payment initialization failed: " . $response['message'];
                log_payment_event('payment_init_failed', [
                    'booking_id' => $booking_id,
                    'error' => $response['message']
                ]);
            }

        } catch (PDOException $e) {
            error_log("Payment initialization error: " . $e->getMessage());
            $payment_error = "Payment initialization failed. Please try again.";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OfficeLink - Payment</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">OfficeLink</div>
            <nav>
                <div class="hamburger" onclick="toggleMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <ul id="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="index.php#features">Features</a></li>
                    <li><a href="index.php#about">About</a></li>
                    <li><a href="search.php">Search</a></li>
                    <li><a href="dashboard.php">Dashboard</a></li>
                    <li><a href="dashboard.php?logout=1">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Payment Section -->
    <section id="payment" class="payment-section">
        <div class="container">
            <div class="payment-container">
                <h2>Complete Your Payment</h2>
                
                <?php if ($payment_error): ?>
                    <div class="error"><?php echo htmlspecialchars($payment_error); ?></div>
                <?php endif; ?>

                <?php if ($payment_success): ?>
                    <div class="success"><?php echo htmlspecialchars($payment_success); ?></div>
                <?php endif; ?>

                <?php if ($booking): ?>
                    <div class="booking-summary">
                        <h3>Booking Summary</h3>
                        <div class="summary-item">
                            <span>Workspace:</span>
                            <span><?php echo htmlspecialchars($booking['workspace_name']); ?></span>
                        </div>
                        <div class="summary-item">
                            <span>Location:</span>
                            <span><?php echo htmlspecialchars($booking['location']); ?></span>
                        </div>
                        <div class="summary-item">
                            <span>Booking Type:</span>
                            <span><?php echo htmlspecialchars(ucfirst($booking['booking_type'])); ?></span>
                        </div>
                        <div class="summary-item">
                            <span>Date:</span>
                            <span><?php echo htmlspecialchars($booking['booking_date']); ?></span>
                        </div>
                        <?php if ($booking['start_time'] && $booking['end_time']): ?>
                            <div class="summary-item">
                                <span>Time:</span>
                                <span><?php echo date('H:i', strtotime($booking['start_time'])) . ' - ' . date('H:i', strtotime($booking['end_time'])); ?></span>
                            </div>
                        <?php endif; ?>
                        <div class="summary-item total">
                            <span>Total Amount:</span>
                            <span><?php echo number_format($booking['total_cost'], 2); ?> ETB</span>
                        </div>
                    </div>

                    <div class="payment-form">
                        <h3>Payment Information</h3>
                        <p>You will be redirected to Chapa's secure payment page to complete your transaction.</p>
                        
                        <form method="POST">
                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                            <button type="submit" name="pay_now" class="btn-payment">
                                Pay <?php echo number_format($booking['total_cost'], 2); ?> ETB with Chapa
                            </button>
                        </form>
                        
                        <div class="payment-security">
                            <p><i>🔒</i> Your payment is secured by Chapa</p>
                            <p>Accepted payment methods: Bank Transfer, Mobile Money, Cards</p>
                        </div>
                    </div>
                <?php endif; ?>

                <div class="payment-actions">
                    <a href="dashboard.php" class="btn-secondary">Back to Dashboard</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>OfficeLink</h4>
                    <p>Flexible workspaces for everyone.</p>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +251 123 456 78</p>
                </div>
            </div>
            <p class="footer-bottom">&copy; <?php echo date("Y"); ?> OfficeLink. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/common.js"></script>
</body>
</html>
