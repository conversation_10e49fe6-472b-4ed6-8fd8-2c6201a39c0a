# OfficeLink Setup and Testing Guide

## Initial Setup

### 1. Database Setup
1. Create a MySQL database named `officelink`
2. Run the SQL script in `database_schema.sql` to create tables and insert sample data
3. Update database credentials in `db_connect.php` if needed

### 2. File Permissions
```bash
chmod 755 uploads/
chmod 755 logs/
chmod 644 .htaccess
chmod 644 uploads/.htaccess
```

### 3. PHP Configuration
Ensure the following PHP extensions are enabled:
- PDO
- PDO_MySQL
- GD (for image processing)
- fileinfo (for MIME type detection)

## Testing Checklist

### Authentication System
- [ ] User registration with password hashing
- [ ] User login with proper validation
- [ ] Session management and security
- [ ] CSRF protection on all forms
- [ ] Rate limiting for login attempts
- [ ] Admin vs regular user access control

### Booking System
- [ ] Workspace search and filtering
- [ ] Booking creation with validation
- [ ] Different booking types (hourly, half-day, full-day, monthly)
- [ ] Cost calculation accuracy
- [ ] Booking status management
- [ ] User dashboard showing bookings

### Admin Panel
- [ ] Workspace management (add, edit, delete)
- [ ] User management
- [ ] Booking management
- [ ] File upload security
- [ ] Image validation and processing

### Security Features
- [ ] Input sanitization and validation
- [ ] SQL injection prevention
- [ ] XSS protection
- [ ] File upload security
- [ ] CSRF token validation
- [ ] Security headers
- [ ] Error handling without information disclosure

### User Interface
- [ ] Responsive design on mobile devices
- [ ] Form validation (client and server-side)
- [ ] Error and success message display
- [ ] Navigation functionality
- [ ] Image display and handling

## Test Scenarios

### 1. User Registration and Login
```
1. Register a new user with valid data
2. Try to register with existing email (should fail)
3. Try to register with weak password (should fail)
4. Login with correct credentials
5. Login with incorrect credentials (should fail)
6. Test rate limiting by attempting multiple failed logins
```

### 2. Booking Flow
```
1. Search for workspaces without login
2. Login and search for workspaces
3. Book a workspace for different time periods
4. Try to book with invalid data
5. View bookings in dashboard
6. Test booking cost calculations
```

### 3. Admin Functions
```
1. Login as admin (<EMAIL> / admin123)
2. Add a new workspace with image
3. Edit existing workspace
4. Delete workspace
5. Manage user accounts
6. Update booking statuses
```

### 4. Security Testing
```
1. Try SQL injection in search forms
2. Try XSS in form inputs
3. Upload malicious files
4. Access admin pages without permission
5. Test CSRF protection by removing tokens
```

## Common Issues and Solutions

### Database Connection Issues
- Check database credentials in `db_connect.php`
- Ensure MySQL service is running
- Verify database exists and user has proper permissions

### File Upload Issues
- Check `uploads/` directory permissions (755)
- Verify PHP upload settings (upload_max_filesize, post_max_size)
- Ensure GD extension is enabled

### Session Issues
- Check PHP session configuration
- Verify session directory permissions
- Clear browser cookies if needed

### Permission Errors
- Set proper file permissions on logs/ and uploads/ directories
- Ensure web server can write to these directories

## Performance Optimization

### Recommended Settings
```php
// In php.ini or .htaccess
upload_max_filesize = 5M
post_max_size = 6M
max_execution_time = 30
memory_limit = 128M
```

### Database Optimization
- Add indexes on frequently queried columns
- Regular database maintenance
- Monitor slow queries

## Security Recommendations

### Production Deployment
1. Change default admin credentials
2. Enable HTTPS
3. Set up regular backups
4. Monitor error logs
5. Keep PHP and MySQL updated
6. Use environment variables for sensitive data

### Monitoring
- Check `logs/security.log` for security events
- Monitor `logs/application_errors.log` for application issues
- Review `logs/database_errors.log` for database problems

## Troubleshooting

### Debug Mode
To enable debug mode, add this to the top of `db_connect.php`:
```php
define('DEBUG_MODE', true);
```

### Log Files
- `logs/application.log` - General application events
- `logs/application_errors.log` - Application errors
- `logs/database_errors.log` - Database errors
- `logs/security.log` - Security events
- `logs/debug.log` - Debug information (when enabled)

### Common Error Messages
- "Connection failed" - Database connection issue
- "Invalid request" - CSRF token missing or invalid
- "Access denied" - Insufficient permissions
- "File upload failed" - File upload or permission issue

## Testing URLs

### Public Pages
- `/` - Homepage
- `/search.php` - Workspace search
- `/login.php` - User login
- `/signup.php` - User registration

### User Pages (requires login)
- `/dashboard.php` - User dashboard
- `/book.php?workspace_id=1` - Book workspace

### Admin Pages (requires admin login)
- `/admin_dashboard.php` - Admin dashboard
- `/admin_workspaces.php` - Manage workspaces
- `/admin_bookings.php` - Manage bookings
- `/admin_users.php` - Manage users

## Final Validation Steps

1. Test all forms with valid and invalid data
2. Verify all security measures are working
3. Check responsive design on different devices
4. Test file upload functionality
5. Verify error handling and logging
6. Test admin and user access controls
7. Validate booking calculations and flow
8. Check database integrity and relationships
