<?php
require_once 'db_connect.php';
require_once 'chapa_config.php';
require_login();

$payment_status = '';
$booking_details = null;
$payment_details = null;

// Get transaction reference from URL
$tx_ref = isset($_GET['tx_ref']) ? sanitize_input($_GET['tx_ref']) : '';

if ($tx_ref) {
    try {
        // Verify payment with Chapa
        $chapa = new ChapaPayment();
        $verification = $chapa->verify_payment($tx_ref);
        
        if ($verification['success']) {
            $chapa_data = $verification['data']['data'];
            $chapa_status = $chapa_data['status'] ?? '';
            
            // Get payment and booking details from database
            $stmt = $pdo->prepare("SELECT p.*, b.*, u.full_name, u.email 
                                  FROM payments p 
                                  JOIN bookings b ON p.booking_id = b.id 
                                  JOIN users u ON p.user_id = u.id 
                                  WHERE p.tx_ref = ? AND p.user_id = ?");
            $stmt->execute([$tx_ref, $_SESSION['user_id']]);
            $result = $stmt->fetch();
            
            if ($result) {
                $payment_details = $result;
                $booking_details = $result;
                
                // Update payment status based on Chapa verification
                $payment_status_db = '';
                $booking_status_db = '';
                $payment_status_display = '';
                
                switch (strtolower($chapa_status)) {
                    case 'success':
                    case 'successful':
                        $payment_status_db = PAYMENT_STATUS_SUCCESS;
                        $booking_status_db = 'confirmed';
                        $payment_status_display = 'success';
                        break;
                    case 'failed':
                    case 'failure':
                        $payment_status_db = PAYMENT_STATUS_FAILED;
                        $booking_status_db = 'cancelled';
                        $payment_status_display = 'failed';
                        break;
                    case 'cancelled':
                        $payment_status_db = PAYMENT_STATUS_CANCELLED;
                        $booking_status_db = 'cancelled';
                        $payment_status_display = 'cancelled';
                        break;
                    default:
                        $payment_status_db = PAYMENT_STATUS_PENDING;
                        $booking_status_db = 'pending';
                        $payment_status_display = 'pending';
                }
                
                // Update database if status changed
                if ($payment_details['status'] !== $payment_status_db) {
                    $stmt = $pdo->prepare("UPDATE payments SET 
                                          status = ?, 
                                          chapa_reference = ?, 
                                          chapa_response = ?, 
                                          updated_at = NOW() 
                                          WHERE tx_ref = ?");
                    $stmt->execute([
                        $payment_status_db,
                        $chapa_data['reference'] ?? '',
                        json_encode($chapa_data),
                        $tx_ref
                    ]);
                    
                    $stmt = $pdo->prepare("UPDATE bookings SET 
                                          status = ?, 
                                          payment_status = ?, 
                                          updated_at = NOW() 
                                          WHERE id = ?");
                    $stmt->execute([
                        $booking_status_db,
                        $payment_status_display === 'success' ? 'paid' : 'unpaid',
                        $booking_details['booking_id']
                    ]);
                }
                
                $payment_status = $payment_status_display;
                
                // Log the verification
                log_payment_event('payment_verified', [
                    'tx_ref' => $tx_ref,
                    'status' => $payment_status,
                    'chapa_status' => $chapa_status
                ]);
                
            } else {
                $payment_status = 'not_found';
            }
            
        } else {
            $payment_status = 'verification_failed';
            log_payment_event('verification_failed', [
                'tx_ref' => $tx_ref,
                'error' => $verification['message']
            ]);
        }
        
    } catch (Exception $e) {
        error_log("Payment verification error: " . $e->getMessage());
        $payment_status = 'error';
    }
} else {
    $payment_status = 'no_reference';
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OfficeLink - Payment Status</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">OfficeLink</div>
            <nav>
                <div class="hamburger" onclick="toggleMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <ul id="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="index.php#features">Features</a></li>
                    <li><a href="index.php#about">About</a></li>
                    <li><a href="search.php">Search</a></li>
                    <li><a href="dashboard.php">Dashboard</a></li>
                    <li><a href="dashboard.php?logout=1">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Payment Status Section -->
    <section id="payment-status" class="payment-status-section">
        <div class="container">
            <div class="payment-status-container">
                
                <?php if ($payment_status === 'success'): ?>
                    <div class="status-success">
                        <div class="status-icon">✅</div>
                        <h2>Payment Successful!</h2>
                        <p>Your booking has been confirmed and payment processed successfully.</p>
                        
                        <?php if ($booking_details): ?>
                            <div class="booking-confirmation">
                                <h3>Booking Confirmation</h3>
                                <div class="confirmation-details">
                                    <p><strong>Booking Reference:</strong> #<?php echo $booking_details['booking_id']; ?></p>
                                    <p><strong>Transaction Reference:</strong> <?php echo htmlspecialchars($tx_ref); ?></p>
                                    <p><strong>Workspace:</strong> <?php echo htmlspecialchars($booking_details['workspace_name']); ?></p>
                                    <p><strong>Location:</strong> <?php echo htmlspecialchars($booking_details['location']); ?></p>
                                    <p><strong>Date:</strong> <?php echo htmlspecialchars($booking_details['booking_date']); ?></p>
                                    <p><strong>Amount Paid:</strong> <?php echo number_format($booking_details['total_cost'], 2); ?> ETB</p>
                                </div>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                <?php elseif ($payment_status === 'failed'): ?>
                    <div class="status-error">
                        <div class="status-icon">❌</div>
                        <h2>Payment Failed</h2>
                        <p>Unfortunately, your payment could not be processed. Please try again.</p>
                        <?php if ($booking_details): ?>
                            <a href="payment.php?booking_id=<?php echo $booking_details['booking_id']; ?>" class="btn-primary">Try Again</a>
                        <?php endif; ?>
                    </div>
                    
                <?php elseif ($payment_status === 'cancelled'): ?>
                    <div class="status-warning">
                        <div class="status-icon">⚠️</div>
                        <h2>Payment Cancelled</h2>
                        <p>You cancelled the payment process. You can try again when ready.</p>
                        <?php if ($booking_details): ?>
                            <a href="payment.php?booking_id=<?php echo $booking_details['booking_id']; ?>" class="btn-primary">Try Again</a>
                        <?php endif; ?>
                    </div>
                    
                <?php elseif ($payment_status === 'pending'): ?>
                    <div class="status-pending">
                        <div class="status-icon">⏳</div>
                        <h2>Payment Pending</h2>
                        <p>Your payment is being processed. Please wait a moment and refresh this page.</p>
                        <button onclick="location.reload()" class="btn-secondary">Refresh Page</button>
                    </div>
                    
                <?php else: ?>
                    <div class="status-error">
                        <div class="status-icon">❓</div>
                        <h2>Payment Status Unknown</h2>
                        <p>We couldn't verify your payment status. Please contact support if you believe this is an error.</p>
                    </div>
                <?php endif; ?>

                <div class="payment-actions">
                    <a href="dashboard.php" class="btn-primary">Go to Dashboard</a>
                    <a href="search.php" class="btn-secondary">Book Another Workspace</a>
                </div>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>OfficeLink</h4>
                    <p>Flexible workspaces for everyone.</p>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +251 123 456 78</p>
                </div>
            </div>
            <p class="footer-bottom">&copy; <?php echo date("Y"); ?> OfficeLink. All rights reserved.</p>
        </div>
    </footer>

    <script src="js/common.js"></script>
</body>
</html>
