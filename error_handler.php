<?php
// OfficeLink Error Handling and Logging System

// Error logging configuration
ini_set('log_errors', 1);
ini_set('error_log', 'logs/php_errors.log');

// Create logs directory if it doesn't exist
if (!is_dir('logs')) {
    mkdir('logs', 0755, true);
}

// Custom error handler
function custom_error_handler($errno, $errstr, $errfile, $errline) {
    $error_types = [
        E_ERROR => 'Fatal Error',
        E_WARNING => 'Warning',
        E_PARSE => 'Parse Error',
        E_NOTICE => 'Notice',
        E_CORE_ERROR => 'Core Error',
        E_CORE_WARNING => 'Core Warning',
        E_COMPILE_ERROR => 'Compile Error',
        E_COMPILE_WARNING => 'Compile Warning',
        E_USER_ERROR => 'User Error',
        E_USER_WARNING => 'User Warning',
        E_USER_NOTICE => 'User Notice',
        E_STRICT => 'Strict Notice',
        E_RECOVERABLE_ERROR => 'Recoverable Error',
        E_DEPRECATED => 'Deprecated',
        E_USER_DEPRECATED => 'User Deprecated'
    ];

    $error_type = $error_types[$errno] ?? 'Unknown Error';
    
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => $error_type,
        'message' => $errstr,
        'file' => $errfile,
        'line' => $errline,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
    ];

    // Log to file
    file_put_contents('logs/application_errors.log', json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);

    // Don't execute PHP internal error handler
    return true;
}

// Custom exception handler
function custom_exception_handler($exception) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => 'Uncaught Exception',
        'message' => $exception->getMessage(),
        'file' => $exception->getFile(),
        'line' => $exception->getLine(),
        'trace' => $exception->getTraceAsString(),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
    ];

    // Log to file
    file_put_contents('logs/application_errors.log', json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);

    // Show user-friendly error page
    show_error_page('An unexpected error occurred. Please try again later.');
}

// Fatal error handler
function custom_fatal_error_handler() {
    $error = error_get_last();
    if ($error && in_array($error['type'], [E_ERROR, E_CORE_ERROR, E_COMPILE_ERROR, E_PARSE])) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'type' => 'Fatal Error',
            'message' => $error['message'],
            'file' => $error['file'],
            'line' => $error['line'],
            'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
        ];

        // Log to file
        file_put_contents('logs/application_errors.log', json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);

        // Show user-friendly error page
        show_error_page('A critical error occurred. Please contact support.');
    }
}

// Show user-friendly error page
function show_error_page($message = 'An error occurred') {
    // Clear any output buffer
    if (ob_get_level()) {
        ob_clean();
    }

    http_response_code(500);
    
    // Check if we're in an AJAX request
    if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
        header('Content-Type: application/json');
        echo json_encode(['error' => $message]);
        exit;
    }

    // Show HTML error page
    ?>
    <!DOCTYPE html>
    <html lang="en">
    <head>
        <meta charset="UTF-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Error - OfficeLink</title>
        <link rel="stylesheet" href="styles.css">
    </head>
    <body>
        <div class="error-page">
            <div class="container">
                <h1>Oops! Something went wrong</h1>
                <p><?php echo htmlspecialchars($message); ?></p>
                <a href="index.php" class="btn-primary">Go Home</a>
            </div>
        </div>
    </body>
    </html>
    <?php
    exit;
}

// Database error handler
function handle_database_error($e, $user_message = 'Database operation failed') {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'type' => 'Database Error',
        'message' => $e->getMessage(),
        'code' => $e->getCode(),
        'file' => $e->getFile(),
        'line' => $e->getLine(),
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
    ];

    // Log to file
    file_put_contents('logs/database_errors.log', json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);

    // Return user-friendly message
    return $user_message;
}

// Application logger
function log_application_event($level, $message, $context = []) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'level' => strtoupper($level),
        'message' => $message,
        'context' => $context,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_id' => $_SESSION['user_id'] ?? null,
        'request_uri' => $_SERVER['REQUEST_URI'] ?? 'unknown'
    ];

    $log_file = 'logs/application.log';
    file_put_contents($log_file, json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);
}

// Validation error handler
function handle_validation_errors($errors) {
    if (empty($errors)) {
        return '';
    }

    if (is_array($errors)) {
        return implode('<br>', array_map('htmlspecialchars', $errors));
    }

    return htmlspecialchars($errors);
}

// Debug function (only in development)
function debug_log($data, $label = 'DEBUG') {
    if (defined('DEBUG_MODE') && DEBUG_MODE) {
        $log_entry = [
            'timestamp' => date('Y-m-d H:i:s'),
            'label' => $label,
            'data' => is_string($data) ? $data : print_r($data, true)
        ];

        file_put_contents('logs/debug.log', json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);
    }
}

// Set error handlers
set_error_handler('custom_error_handler');
set_exception_handler('custom_exception_handler');
register_shutdown_function('custom_fatal_error_handler');

// Set display_errors based on environment
if (defined('PRODUCTION') && PRODUCTION) {
    ini_set('display_errors', 0);
    ini_set('display_startup_errors', 0);
} else {
    ini_set('display_errors', 1);
    ini_set('display_startup_errors', 1);
    define('DEBUG_MODE', true);
}

// Log application start
log_application_event('info', 'Application started', [
    'script' => $_SERVER['SCRIPT_NAME'] ?? 'unknown',
    'method' => $_SERVER['REQUEST_METHOD'] ?? 'unknown'
]);
