# OfficeLink Security Configuration

# Prevent access to sensitive files
<Files ".htaccess">
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

<Files "*.log">
    Order Allow,Deny
    Deny from all
</Files>

<Files "*.sql">
    Order Allow,<PERSON>y
    Deny from all
</Files>

<Files "*.env">
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

<Files "*.config">
    Order Allow,<PERSON>y
    <PERSON> from all
</Files>

# Security Headers
<IfModule mod_headers.c>
    # Prevent XSS attacks
    Header always set X-XSS-Protection "1; mode=block"
    
    # Prevent MIME type sniffing
    Header always set X-Content-Type-Options "nosniff"
    
    # Prevent clickjacking
    Header always set X-Frame-Options "SAMEORIGIN"
    
    # Referrer Policy
    Header always set Referrer-Policy "strict-origin-when-cross-origin"
    
    # Content Security Policy (basic)
    Header always set Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://fonts.googleapis.com; style-src 'self' 'unsafe-inline' https://fonts.googleapis.com; font-src 'self' https://fonts.gstatic.com; img-src 'self' data:; connect-src 'self';"
</IfModule>

# Disable server signature
ServerSignature Off

# Hide PHP version
<IfModule mod_php7.c>
    php_flag expose_php off
</IfModule>

<IfModule mod_php8.c>
    php_flag expose_php off
</IfModule>

# Prevent access to PHP configuration files
<Files "*.ini">
    Order Allow,Deny
    Deny from all
</Files>

# Limit file upload size (adjust as needed)
<IfModule mod_php7.c>
    php_value upload_max_filesize 5M
    php_value post_max_size 6M
    php_value max_execution_time 30
    php_value max_input_time 30
</IfModule>

<IfModule mod_php8.c>
    php_value upload_max_filesize 5M
    php_value post_max_size 6M
    php_value max_execution_time 30
    php_value max_input_time 30
</IfModule>

# Prevent access to backup files
<FilesMatch "\.(bak|backup|old|orig|save|swp|tmp)$">
    Order Allow,Deny
    Deny from all
</FilesMatch>

# Enable HTTPS redirect (uncomment if using HTTPS)
# RewriteEngine On
# RewriteCond %{HTTPS} off
# RewriteRule ^(.*)$ https://%{HTTP_HOST}%{REQUEST_URI} [L,R=301]

# Prevent directory browsing
Options -Indexes

# Custom error pages (create these files if needed)
# ErrorDocument 404 /404.php
# ErrorDocument 403 /403.php
# ErrorDocument 500 /500.php

# Compress files for better performance
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>

# Cache static files
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/pdf "access plus 1 month"
    ExpiresByType text/javascript "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType application/x-javascript "access plus 1 month"
    ExpiresByType application/x-shockwave-flash "access plus 1 month"
    ExpiresByType image/x-icon "access plus 1 year"
    ExpiresDefault "access plus 2 days"
</IfModule>

# Rate limiting (basic protection against brute force)
<IfModule mod_evasive24.c>
    DOSHashTableSize    2048
    DOSPageCount        10
    DOSPageInterval     1
    DOSSiteCount        50
    DOSSiteInterval     1
    DOSBlockingPeriod   600
</IfModule>
