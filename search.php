<?php
require_once 'db_connect.php';

// Check if the user is logged in (optional for search, but required for bookmarking)
$user_id = isset($_SESSION['user_id']) ? $_SESSION['user_id'] : null;

// Handle search and filter with proper validation
$location = isset($_GET['location']) ? sanitize_input($_GET['location']) : '';
$booking_type = isset($_GET['booking_type']) ? sanitize_input($_GET['booking_type']) : '';
$amenities = isset($_GET['amenities']) ? array_map('sanitize_input', $_GET['amenities']) : [];

// Build the query with proper validation
$query = "SELECT * FROM workspaces WHERE 1=1";
$params = [];

if (!empty($location)) {
    $query .= " AND location LIKE ?";
    $params[] = "%$location%";
}

if (!empty($booking_type) && in_array($booking_type, ['hourly', 'half-day', 'full-day', 'monthly'])) {
    // We'll use the booking type to display the appropriate rate later
}

if (!empty($amenities)) {
    $amenities_conditions = [];
    foreach ($amenities as $amenity) {
        $amenities_conditions[] = "amenities LIKE ?";
        $params[] = "%$amenity%";
    }
    $query .= " AND (" . implode(" OR ", $amenities_conditions) . ")";
}

$stmt = $pdo->prepare($query);
$stmt->execute($params);
$workspaces = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle bookmarking
$bookmark_message = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['bookmark'])) {
    if (!$user_id) {
        header("Location: login.php");
        exit;
    }

    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $bookmark_message = "Invalid request. Please try again.";
    } else {
        $workspace_id = filter_var($_POST['workspace_id'], FILTER_VALIDATE_INT);
        if ($workspace_id === false) {
            $bookmark_message = "Invalid workspace ID.";
        } else {
            try {
                $stmt = $pdo->prepare("INSERT INTO bookmarks (user_id, workspace_id) VALUES (?, ?)");
                $stmt->execute([$user_id, $workspace_id]);
                $bookmark_message = "Workspace bookmarked successfully!";
            } catch (PDOException $e) {
                if ($e->getCode() == 23000) { // Duplicate entry
                    $bookmark_message = "Workspace already bookmarked.";
                } else {
                    error_log("Bookmark error: " . $e->getMessage());
                    $bookmark_message = "Failed to bookmark workspace. Please try again.";
                }
            }
        }
    }
}

// Check if a workspace is bookmarked
$bookmarked_workspaces = [];
if ($user_id) {
    $stmt = $pdo->prepare("SELECT workspace_id FROM bookmarks WHERE user_id = ?");
    $stmt->execute([$user_id]);
    $bookmarked_workspaces = array_column($stmt->fetchAll(PDO::FETCH_ASSOC), 'workspace_id');
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OfficeLink - Search Workspaces</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">OfficeLink</div>
            <nav>
                <div class="hamburger" onclick="toggleMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <ul id="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="index.php#features">Features</a></li>
                    <li><a href="index.php#about">About</a></li>
                    <?php if ($user_id): ?>
                        <li><a href="dashboard.php">Dashboard</a></li>
                        <li><a href="dashboard.php?logout=true">Logout</a></li>
                    <?php else: ?>
                        <li><a href="signup.php" class="btn-signup">Sign Up</a></li>
                        <li><a href="login.php">Login</a></li>
                    <?php endif; ?>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Search Section -->
    <section id="search" class="search-section">
        <div class="container">
            <h2>Find Your Perfect Workspace</h2>
            <?php if (!empty($bookmark_message)): ?>
                <div class="message <?php echo strpos($bookmark_message, 'successfully') !== false ? 'success' : 'error'; ?>">
                    <?php echo htmlspecialchars($bookmark_message); ?>
                </div>
            <?php endif; ?>
            <form method="GET" class="search-form">
                <div class="form-group">
                    <label for="location">Location</label>
                    <input type="text" id="location" name="location" value="<?php echo htmlspecialchars($location); ?>" placeholder="e.g., Addis Ababa">
                </div>
                <div class="form-group">
                    <label for="booking_type">Booking Type</label>
                    <select id="booking_type" name="booking_type">
                        <option value="">Any</option>
                        <option value="hourly" <?php echo $booking_type === 'hourly' ? 'selected' : ''; ?>>Hourly</option>
                        <option value="half-day" <?php echo $booking_type === 'half-day' ? 'selected' : ''; ?>>Half-Day</option>
                        <option value="full-day" <?php echo $booking_type === 'full-day' ? 'selected' : ''; ?>>Full-Day</option>
                        <option value="monthly" <?php echo $booking_type === 'monthly' ? 'selected' : ''; ?>>Monthly</option>
                    </select>
                </div>
                <div class="form-group">
                    <label>Amenities</label>
                    <div class="checkbox-group">
                        <label><input type="checkbox" name="amenities[]" value="Wi-Fi" <?php echo in_array('Wi-Fi', $amenities) ? 'checked' : ''; ?>> Wi-Fi</label>
                        <label><input type="checkbox" name="amenities[]" value="ergonomic chairs" <?php echo in_array('ergonomic chairs', $amenities) ? 'checked' : ''; ?>> Ergonomic Chairs</label>
                        <label><input type="checkbox" name="amenities[]" value="free snacks" <?php echo in_array('free snacks', $amenities) ? 'checked' : ''; ?>> Free Snacks</label>
                        <label><input type="checkbox" name="amenities[]" value="projector" <?php echo in_array('projector', $amenities) ? 'checked' : ''; ?>> Projector</label>
                        <label><input type="checkbox" name="amenities[]" value="coffee machine" <?php echo in_array('coffee machine', $amenities) ? 'checked' : ''; ?>> Coffee Machine</label>
                        <label><input type="checkbox" name="amenities[]" value="whiteboard" <?php echo in_array('whiteboard', $amenities) ? 'checked' : ''; ?>> Whiteboard</label>
                    </div>
                </div>
                <button type="submit" class="btn-primary">Search</button>
            </form>

            <!-- Search Results -->
            <div class="workspace-grid">
                <?php if (empty($workspaces)): ?>
                    <p>No workspaces found matching your criteria.</p>
                <?php else: ?>
                    <?php foreach ($workspaces as $workspace): ?>
                        <div class="workspace-card">
                            <img src="<?php echo htmlspecialchars($workspace['image_url']); ?>" alt="<?php echo htmlspecialchars($workspace['name']); ?>">
                            <div class="workspace-details">
                                <h3><?php echo htmlspecialchars($workspace['name']); ?></h3>
                                <p><strong>Location:</strong> <?php echo htmlspecialchars($workspace['location']); ?></p>
                                <p><strong>Description:</strong> <?php echo htmlspecialchars($workspace['description']); ?></p>
                                <p><strong>Noise Level:</strong> <?php echo htmlspecialchars($workspace['noise_level']); ?></p>
                                <p><strong>Amenities:</strong> <?php echo htmlspecialchars($workspace['amenities']); ?></p>
                                <p><strong>Pricing:</strong></p>
                                <ul>
                                    <li>Hourly: $<?php echo number_format($workspace['hourly_rate'], 2); ?></li>
                                    <li>Half-Day: $<?php echo number_format($workspace['half_day_rate'], 2); ?></li>
                                    <li>Full-Day: $<?php echo number_format($workspace['full_day_rate'], 2); ?></li>
                                    <li>Monthly: $<?php echo number_format($workspace['monthly_rate'], 2); ?></li>
                                </ul>
                                <div class="workspace-actions">
                                    <a href="book.php?workspace_id=<?php echo $workspace['id']; ?>" class="btn-primary">Book Now</a>
                                    <?php if ($user_id): ?>
                                        <form method="POST" style="display:inline;">
                                            <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                                            <input type="hidden" name="workspace_id" value="<?php echo $workspace['id']; ?>">
                                            <button type="submit" name="bookmark" class="btn-bookmark <?php echo in_array($workspace['id'], $bookmarked_workspaces) ? 'bookmarked' : ''; ?>" <?php echo in_array($workspace['id'], $bookmarked_workspaces) ? 'disabled' : ''; ?>>
                                                <?php echo in_array($workspace['id'], $bookmarked_workspaces) ? 'Bookmarked' : 'Bookmark'; ?>
                                            </button>
                                        </form>
                                    <?php else: ?>
                                        <a href="login.php" class="btn-bookmark">Login to Bookmark</a>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    <?php endforeach; ?>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>OfficeLink</h4>
                    <p>Flexible workspaces for everyone.</p>
                </div>
                <div class="footer-section">
                    <h4>Links</h4>
                    <ul>
                        <li><a href="index.php#features">Features</a></li>
                        <li><a href="index.php#about">About</a></li>
                        <li><a href="signup.php">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +251 123 456 78</p>
                </div>
            </div>
            <p class="footer-bottom"> <?php echo date("Y"); ?> OfficeLink. All rights reserved.</p>
        </div>
    </footer>

    <script>
        function toggleMenu() {
            const menu = document.getElementById('nav-menu');
            const hamburger = document.querySelector('.hamburger');
            menu.classList.toggle('active');
            hamburger.classList.toggle('active');
        }
    </script>
</body>
</html>