<?php
require_once 'db_connect.php';
require_once 'chapa_config.php';

// Get transaction reference and amount from URL
$tx_ref = isset($_GET['tx_ref']) ? sanitize_input($_GET['tx_ref']) : '';
$amount = isset($_GET['amount']) ? sanitize_input($_GET['amount']) : '';

if (empty($tx_ref) || empty($amount)) {
    header("Location: dashboard.php");
    exit;
}

// Handle demo payment submission
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $payment_action = $_POST['payment_action'] ?? '';
    
    if ($payment_action === 'success') {
        // Simulate successful payment
        $status = 'success';
    } elseif ($payment_action === 'failed') {
        // Simulate failed payment
        $status = 'failed';
    } else {
        // Simulate cancelled payment
        $status = 'cancelled';
    }
    
    // Update payment status in database
    try {
        $stmt = $pdo->prepare("UPDATE payments SET status = ?, chapa_reference = ?, updated_at = NOW() WHERE tx_ref = ?");
        $stmt->execute([$status, 'demo_' . $tx_ref, $tx_ref]);
        
        // Update booking status
        try {
            if ($status === 'success') {
                // Try with payment_status column first
                try {
                    $stmt = $pdo->prepare("UPDATE bookings b
                                          JOIN payments p ON b.id = p.booking_id
                                          SET b.status = 'confirmed', b.payment_status = 'paid'
                                          WHERE p.tx_ref = ?");
                    $stmt->execute([$tx_ref]);
                } catch (PDOException $e) {
                    // Fallback without payment_status column
                    $stmt = $pdo->prepare("UPDATE bookings b
                                          JOIN payments p ON b.id = p.booking_id
                                          SET b.status = 'confirmed'
                                          WHERE p.tx_ref = ?");
                    $stmt->execute([$tx_ref]);
                }
            } else {
                // Try with payment_status column first
                try {
                    $stmt = $pdo->prepare("UPDATE bookings b
                                          JOIN payments p ON b.id = p.booking_id
                                          SET b.status = 'cancelled', b.payment_status = 'unpaid'
                                          WHERE p.tx_ref = ?");
                    $stmt->execute([$tx_ref]);
                } catch (PDOException $e) {
                    // Fallback without payment_status column
                    $stmt = $pdo->prepare("UPDATE bookings b
                                          JOIN payments p ON b.id = p.booking_id
                                          SET b.status = 'cancelled'
                                          WHERE p.tx_ref = ?");
                    $stmt->execute([$tx_ref]);
                }
            }
        } catch (PDOException $e) {
            error_log("Demo payment booking update error: " . $e->getMessage());
        }
        
        // Log the demo payment
        log_payment_event('demo_payment_' . $status, [
            'tx_ref' => $tx_ref,
            'amount' => $amount,
            'status' => $status
        ]);
        
    } catch (PDOException $e) {
        error_log("Demo payment error: " . $e->getMessage());
    }
    
    // Redirect to success page
    header("Location: payment_success.php?tx_ref=" . urlencode($tx_ref));
    exit;
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Demo Payment - Chapa Simulation</title>
    <link rel="stylesheet" href="styles.css">
    <style>
        .demo-payment {
            max-width: 500px;
            margin: 50px auto;
            padding: 30px;
            background: white;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            text-align: center;
        }
        .demo-notice {
            background: #fff3cd;
            color: #856404;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            border: 1px solid #ffeaa7;
        }
        .payment-amount {
            font-size: 24px;
            font-weight: bold;
            color: #667eea;
            margin: 20px 0;
        }
        .demo-buttons {
            display: flex;
            gap: 10px;
            justify-content: center;
            flex-wrap: wrap;
            margin-top: 20px;
        }
        .demo-btn {
            padding: 12px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            font-weight: 600;
            text-decoration: none;
            display: inline-block;
            min-width: 120px;
        }
        .btn-success {
            background: #28a745;
            color: white;
        }
        .btn-danger {
            background: #dc3545;
            color: white;
        }
        .btn-warning {
            background: #ffc107;
            color: #212529;
        }
        .demo-btn:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        .payment-details {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
            text-align: left;
        }
    </style>
</head>
<body>
    <div class="demo-payment">
        <h2>🧪 Demo Payment Gateway</h2>
        
        <div class="demo-notice">
            <strong>Demo Mode:</strong> This is a simulation of Chapa payment gateway. 
            No real money will be charged.
        </div>
        
        <div class="payment-details">
            <p><strong>Transaction Reference:</strong> <?php echo htmlspecialchars($tx_ref); ?></p>
            <p><strong>Amount:</strong> <?php echo htmlspecialchars($amount); ?> ETB</p>
            <p><strong>Merchant:</strong> OfficeLink (Demo)</p>
        </div>
        
        <div class="payment-amount">
            <?php echo htmlspecialchars($amount); ?> ETB
        </div>
        
        <p>Choose how you want to simulate the payment:</p>
        
        <div class="demo-buttons">
            <form method="POST" style="display: inline;">
                <input type="hidden" name="payment_action" value="success">
                <button type="submit" class="demo-btn btn-success">
                    ✅ Simulate Success
                </button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="payment_action" value="failed">
                <button type="submit" class="demo-btn btn-danger">
                    ❌ Simulate Failure
                </button>
            </form>
            
            <form method="POST" style="display: inline;">
                <input type="hidden" name="payment_action" value="cancelled">
                <button type="submit" class="demo-btn btn-warning">
                    🚫 Simulate Cancel
                </button>
            </form>
        </div>
        
        <div style="margin-top: 30px; padding-top: 20px; border-top: 1px solid #eee;">
            <p style="font-size: 12px; color: #666;">
                In production, this would be the real Chapa payment page where customers 
                enter their payment details.
            </p>
            <a href="dashboard.php" style="color: #667eea; text-decoration: none;">
                ← Back to Dashboard
            </a>
        </div>
    </div>
</body>
</html>
