* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: 'Poppins', sans-serif;
}

body {
    line-height: 1.6;
    color: #333;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 15px;
}

/* Header */
header {
    background: #fff;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
    padding: 15px 0;
    position: fixed;
    width: 100%;
    top: 0;
    z-index: 100;
}

header .container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
}

.logo {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
}

nav ul {
    list-style: none;
    display: flex;
    gap: 15px;
}

nav ul li a {
    text-decoration: none;
    color: #2c3e50;
    font-weight: 500;
}

.btn-signup {
    color: #000000;
    padding: 8px 16px;
    border-radius: 5px;
}

/* Hamburger Menu */
.hamburger {
    display: none; /* Hidden by default on larger screens */
    flex-direction: column;
    justify-content: space-between;
    width: 30px;
    height: 20px;
    cursor: pointer;
}

.hamburger span {
    background: #2c3e50;
    height: 3px;
    width: 100%;
    border-radius: 2px;
    transition: all 0.3s ease;
}

.hamburger.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.hamburger.active span:nth-child(2) {
    opacity: 0;
}

.hamburger.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -7px);
}

/* Responsive Design */
@media (max-width: 768px) {
    /* Header */
    header .container {
        flex-direction: row;
        align-items: center;
    }

    .hamburger {
        display: flex; /* Show hamburger on mobile */
    }

    nav ul {
        display: none; /* Hide menu by default on mobile */
        flex-direction: column;
        gap: 10px;
        width: 100%;
        background: #fff;
        position: absolute;
        top: 60px;
        left: 0;
        padding: 20px;
        box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    }

    nav ul.active {
        display: flex; /* Show menu when active */
    }

    nav ul li {
        width: 100%;
        text-align: center;
    }

    nav ul li a {
        display: block;
        padding: 10px;
        border-bottom: 1px solid #eee;
    }

    .btn-signup {
        display: block;
        width: fit-content;
        margin: 0 auto;
    }
}

@media (max-width: 480px) {
    .logo {
        font-size: 20px;
    }
}

/* Hero Section */
.hero {
    padding: 100px 0 40px;
    background: #f5f7fa;
}

.hero .container {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    align-items: center;
}

.hero-content {
    flex: 1;
    min-width: 300px;
    text-align: center;
    padding: 20px;
}

.hero-content h1 {
    font-size: 36px;
    color: #2c3e50;
    margin-bottom: 20px;
}

.hero-content p {
    font-size: 16px;
    margin-bottom: 20px;
}

.btn-primary {
    background: #3498db;
    color: #fff;
    padding: 10px 20px;
    text-decoration: none;
    border-radius: 5px;
    font-weight: 600;
    display: inline-block;
}

.hero-image {
    flex: 1;
    min-width: 300px;
    padding: 20px;
}

.hero-image img {
    max-width: 100%;
    height: auto;
    border-radius: 10px;
}

/* Features Section */
.features {
    padding: 40px 0;
    text-align: center;
}

.features h2 {
    font-size: 32px;
    margin-bottom: 30px;
}

.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
}

.feature-item img {
    margin-bottom: 15px;
}

.feature-item img {
    width: 40px; /* Set a smaller width */
    height: 40px; /* Set a smaller height */
    margin-bottom: 10px; /* Reduced margin to balance the layout */
}

.feature-item h3 {
    font-size: 20px;
    margin-bottom: 10px;
}

.feature-item p {
    color: #7f8c8d;
    font-size: 14px;
}
/* Responsive adjustments */
@media (max-width: 768px) {
    .feature-item img {
        width: 35px; /* Slightly smaller on tablets */
        height: 35px;
    }

    .feature-item h3 {
        font-size: 18px;
    }

    .feature-item p {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .feature-item img {
        width: 30px; /* Even smaller on phones */
        height: 30px;
    }

    .feature-item h3 {
        font-size: 16px;
    }

    .feature-item p {
        font-size: 12px;
    }
}

/* How It Works Section */
.how-it-works {
    padding: 40px 0;
    background: #f5f7fa;
    text-align: center;
}

.how-it-works h2 {
    font-size: 32px;
    margin-bottom: 30px;
}

.steps {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 20px;
}

.step {
    flex: 1;
    min-width: 200px;
    padding: 15px;
}

.step span {
    display: inline-block;
    width: 40px;
    height: 40px;
    background: #3498db;
    color: #fff;
    border-radius: 50%;
    line-height: 40px;
    font-size: 18px;
    margin-bottom: 15px;
}

.step h3 {
    font-size: 18px;
    margin-bottom: 10px;
}

/* Footer */
footer {
    background: #000d1b;
    color: #fff;
    padding: 30px 0 15px;
}

.footer-content {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
    gap: 20px;
    margin-bottom: 15px;
}

.footer-section {
    flex: 1;
    min-width: 200px;
}

.footer-section h4 {
    margin-bottom: 10px;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li a {
    color: #ecf0f1;
    text-decoration: none;
}

.footer-bottom {
    text-align: center;
    font-size: 14px;
    color: #bdc3c7;
}

/* Responsive Design with Media Queries */
@media (max-width: 768px) {
    /* Header */
    header .container {
        flex-direction: column;
        text-align: center;
    }

    nav ul {
        flex-direction: column;
        gap: 10px;
        margin-top: 10px;
        width: 100%;
    }

    nav ul li {
        width: 100%;
    }

    nav ul li a {
        display: block;
        padding: 10px;
        border-bottom: 1px solid #eee;
    }

    .btn-signup {
        display: block;
        width: fit-content;
        margin: 0 auto;
    }

    /* Hero Section */
    .hero {
        padding: 80px 0 20px;
    }

    .hero .container {
        flex-direction: column;
    }

    .hero-content {
        order: 1; /* Ensure content comes first */
        min-width: 100%;
    }

    .hero-content h1 {
        font-size: 28px;
    }

    .hero-content p {
        font-size: 14px;
    }

    .hero-image {
        order: 2; /* Image comes after content */
        min-width: 100%;
        padding: 10px;
    }

    /* Features Section */
    .features h2 {
        font-size: 26px;
    }

    .feature-grid {
        grid-template-columns: 1fr; /* Single column */
    }

    /* How It Works Section */
    .how-it-works h2 {
        font-size: 26px;
    }

    .steps {
        flex-direction: column;
        align-items: center;
    }

    .step {
        min-width: 100%;
    }
}

@media (max-width: 480px) {
    /* Header */
    .logo {
        font-size: 20px;
    }

    /* Hero Section */
    .hero-content h1 {
        font-size: 24px;
    }

    .hero-content p {
        font-size: 14px;
    }

    .btn-primary {
        padding: 8px 16px;
        font-size: 14px;
    }

    /* Features Section */
    .features h2 {
        font-size: 22px;
    }

    .feature-item h3 {
        font-size: 18px;
    }

    .feature-item p {
        font-size: 14px;
    }

    /* How It Works Section */
    .how-it-works h2 {
        font-size: 22px;
    }

    .step h3 {
        font-size: 16px;
    }

    /* Footer */
    .footer-section {
        min-width: 100%;
        text-align: center;
    }
}
/* Auth Section */
.auth-section {
    padding: 120px 0 60px;
    background: #f5f7fa;
    min-height: 100vh;
    display: flex;
    align-items: center;
}

.auth-container {
    max-width: 500px;
    margin: 0 auto;
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
}

.auth-form h2 {
    font-size: 28px;
    margin-bottom: 20px;
    text-align: center;
    color: #2c3e50;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    margin-bottom: 5px;
    color: #2c3e50;
}

.form-group input {
    width: 100%;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ddd;
    border-radius: 5px;
    outline: none;
    transition: border 0.3s;
}

.form-group input:focus {
    border-color: #3498db;
}

.auth-form .btn-primary {
    width: 100%;
    padding: 12px;
    font-size: 16px;
}

.form-footer {
    text-align: center;
    margin-top: 20px;
    font-size: 14px;
}

.form-footer a {
    color: #3498db;
    text-decoration: none;
}

.success, .error, .message {
    padding: 12px 15px;
    border-radius: 5px;
    margin-bottom: 15px;
    text-align: center;
    font-weight: 500;
}

.success {
    color: #155724;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
}

.error {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
}

.message.success {
    color: #155724;
    background-color: #d4edda;
    border: 1px solid #c3e6cb;
}

.message.error {
    color: #721c24;
    background-color: #f8d7da;
    border: 1px solid #f5c6cb;
}

/* Error Page Styles */
.error-page {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    text-align: center;
}

.error-page .container {
    max-width: 600px;
    padding: 40px 20px;
}

.error-page h1 {
    font-size: 48px;
    margin-bottom: 20px;
    font-weight: 600;
}

.error-page p {
    font-size: 18px;
    margin-bottom: 30px;
    opacity: 0.9;
}

.error-page .btn-primary {
    background: rgba(255, 255, 255, 0.2);
    border: 2px solid white;
    color: white;
    padding: 12px 30px;
    text-decoration: none;
    border-radius: 5px;
    display: inline-block;
    transition: all 0.3s ease;
}

.error-page .btn-primary:hover {
    background: white;
    color: #667eea;
}

/* Responsive Design for Auth Section */
@media (max-width: 768px) {
    .auth-section {
        padding: 80px 0 40px;
    }

    .auth-container {
        padding: 20px;
        margin: 0 15px;
    }

    .auth-form h2 {
        font-size: 24px;
    }
}

@media (max-width: 480px) {
    .auth-form h2 {
        font-size: 20px;
    }

    .form-group input {
        font-size: 14px;
        padding: 8px;
    }

    .auth-form .btn-primary {
        font-size: 14px;
        padding: 10px;
    }

    .form-footer {
        font-size: 12px;
    }
}

/* Dashboard Section */
.dashboard-section {
    padding: 120px 0 60px;
    background: #f5f7fa;
    min-height: 100vh;
}

.dashboard-section h2 {
    font-size: 32px;
    margin-bottom: 30px;
    text-align: center;
    color: #2c3e50;
}

.dashboard-card {
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.dashboard-card h3 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #2c3e50;
}

.profile-form .form-group {
    margin-bottom: 20px;
}

.profile-form .form-group label {
    display: block;
    font-size: 14px;
    margin-bottom: 5px;
    color: #2c3e50;
}

.profile-form .form-group input {
    width: 100%;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ddd;
    border-radius: 5px;
    outline: none;
    transition: border 0.3s;
}

.profile-form .form-group input:focus {
    border-color: #3498db;
}

.profile-form .btn-primary {
    width: 100%;
    padding: 12px;
    font-size: 16px;
}

.booking-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.booking-table th,
.booking-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.booking-table th {
    background: #3498db;
    color: #fff;
    font-weight: 600;
}

.booking-table td {
    color: #2c3e50;
}

.booking-table tr:hover {
    background: #f9f9f9;
}

/* Responsive Design for Dashboard */
@media (max-width: 768px) {
    .dashboard-section {
        padding: 80px 0 40px;
    }

    .dashboard-section h2 {
        font-size: 26px;
    }

    .dashboard-card {
        padding: 20px;
    }

    .dashboard-card h3 {
        font-size: 20px;
    }

    .booking-table th,
    .booking-table td {
        padding: 8px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .dashboard-section h2 {
        font-size: 22px;
    }

    .dashboard-card {
        padding: 15px;
    }

    .dashboard-card h3 {
        font-size: 18px;
    }

    .profile-form .form-group input {
        font-size: 14px;
        padding: 8px;
    }

    .profile-form .btn-primary {
        font-size: 14px;
        padding: 10px;
    }

    .booking-table {
        display: block;
        overflow-x: auto; /* Allow horizontal scrolling on small screens */
    }

    .booking-table th,
    .booking-table td {
        font-size: 12px;
        padding: 6px;
    }
}
/* Search Section */
.search-section {
    padding: 120px 0 60px;
    background: #f5f7fa;
    min-height: 100vh;
}

.search-section h2 {
    font-size: 32px;
    margin-bottom: 30px;
    text-align: center;
    color: #2c3e50;
}

.search-form {
    background: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    display: flex;
    flex-wrap: wrap;
    gap: 20px;
}

.search-form .form-group {
    flex: 1;
    min-width: 200px;
}

.search-form .form-group label {
    display: block;
    font-size: 14px;
    margin-bottom: 5px;
    color: #2c3e50;
}

.search-form .form-group input,
.search-form .form-group select {
    width: 100%;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ddd;
    border-radius: 5px;
    outline: none;
    transition: border 0.3s;
}

.search-form .form-group input:focus,
.search-form .form-group select:focus {
    border-color: #3498db;
}

.checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.checkbox-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: #2c3e50;
}

.search-form .btn-primary {
    padding: 10px 20px;
    font-size: 16px;
    align-self: flex-end;
}

.workspace-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
}

.workspace-card {
    background: #fff;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    overflow: hidden;
}

.workspace-card img {
    width: 100%;
    height: 200px;
    object-fit: cover;
}

.workspace-details {
    padding: 20px;
}

.workspace-details h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #2c3e50;
}

.workspace-details p {
    font-size: 14px;
    margin-bottom: 10px;
    color: #7f8c8d;
}

.workspace-details ul {
    list-style: none;
    margin-bottom: 15px;
    font-size: 14px;
    color: #7f8c8d;
}

.workspace-details ul li {
    margin-bottom: 5px;
}

.workspace-actions {
    display: flex;
    gap: 10px;
}

.btn-bookmark {
    background: #ecf0f1;
    color: #2c3e50;
    padding: 10px 15px;
    border: none;
    border-radius: 5px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s;
}

.btn-bookmark.bookmarked {
    background: #3498db;
    color: #fff;
}

/* Responsive Design for Search Section */
@media (max-width: 768px) {
    .search-section {
        padding: 80px 0 40px;
    }

    .search-section h2 {
        font-size: 26px;
    }

    .search-form {
        flex-direction: column;
    }

    .search-form .btn-primary {
        width: 100%;
    }

    .workspace-card img {
        height: 150px;
    }

    .workspace-details h3 {
        font-size: 18px;
    }

    .workspace-details p,
    .workspace-details ul {
        font-size: 13px;
    }
}

@media (max-width: 480px) {
    .search-section h2 {
        font-size: 22px;
    }

    .search-form .form-group input,
    .search-form .form-group select {
        font-size: 14px;
        padding: 8px;
    }

    .checkbox-group label {
        font-size: 12px;
    }

    .workspace-card img {
        height: 120px;
    }

    .workspace-details {
        padding: 15px;
    }

    .workspace-details h3 {
        font-size: 16px;
    }

    .workspace-actions {
        flex-direction: column;
    }

    .workspace-actions .btn-primary,
    .workspace-actions .btn-bookmark {
        width: 100%;
        text-align: center;
    }
}
  
/* Admin Section */
.admin-section {
    padding: 120px 0 60px;
    background: #f5f7fa;
    min-height: 100vh;
}

.admin-section h2 {
    font-size: 32px;
    margin-bottom: 30px;
    text-align: center;
    color: #2c3e50;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.stat-card {
    background: #fff;
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    text-align: center;
}

.stat-card h3 {
    font-size: 20px;
    margin-bottom: 10px;
    color: #2c3e50;
}

.stat-card p {
    font-size: 24px;
    font-weight: 600;
    color: #3498db;
}

.admin-actions {
    display: flex;
    justify-content: center;
    gap: 20px;
    flex-wrap: wrap;
}

.admin-card {
    background: #fff;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
}

.admin-card h3 {
    font-size: 24px;
    margin-bottom: 20px;
    color: #2c3e50;
}

.admin-form .form-group {
    margin-bottom: 20px;
}

.admin-form .form-group label {
    display: block;
    font-size: 14px;
    margin-bottom: 5px;
    color: #2c3e50;
}

.admin-form .form-group input,
.admin-form .form-group select,
.admin-form .form-group textarea {
    width: 100%;
    padding: 10px;
    font-size: 16px;
    border: 1px solid #ddd;
    border-radius: 5px;
    outline: none;
    transition: border 0.3s;
}

.admin-form .form-group input:focus,
.admin-form .form-group select:focus,
.admin-form .form-group textarea:focus {
    border-color: #3498db;
}

.admin-form .form-group textarea {
    resize: vertical;
}

.admin-form .checkbox-group {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
}

.admin-form .checkbox-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-size: 14px;
    color: #2c3e50;
}

.admin-form .btn-primary {
    width: 100%;
    padding: 12px;
    font-size: 16px;
}

.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 20px;
}

.admin-table th,
.admin-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #ddd;
}

.admin-table th {
    background: #3498db;
    color: #fff;
    font-weight: 600;
}

.admin-table td {
    color: #2c3e50;
}

.admin-table tr:hover {
    background: #f9f9f9;
}

.btn-edit,
.btn-delete {
    padding: 5px 10px;
    border-radius: 5px;
    text-decoration: none;
    font-size: 14px;
    margin-right: 5px;
}

.btn-edit {
    background: #3498db;
    color: #fff;
}

.btn-delete {
    background: #e74c3c;
    color: #fff;
}

/* Responsive Design for Admin Section */
@media (max-width: 768px) {
    .admin-section {
        padding: 80px 0 40px;
    }

    .admin-section h2 {
        font-size: 26px;
    }

    .admin-card {
        padding: 20px;
    }

    .admin-card h3 {
        font-size: 20px;
    }

    .admin-form .form-group input,
    .admin-form .form-group select,
    .admin-form .form-group textarea {
        font-size: 14px;
        padding: 8px;
    }

    .admin-form .btn-primary {
        font-size: 14px;
        padding: 10px;
    }

    .admin-table {
        display: block;
        overflow-x: auto;
    }

    .admin-table th,
    .admin-table td {
        padding: 8px;
        font-size: 14px;
    }
}

@media (max-width: 480px) {
    .admin-section h2 {
        font-size: 22px;
    }

    .admin-card {
        padding: 15px;
    }

    .admin-card h3 {
        font-size: 18px;
    }

    .admin-table th,
    .admin-table td {
        font-size: 12px;
        padding: 6px;
    }

    .btn-edit,
    .btn-delete {
        font-size: 12px;
        padding: 4px 8px;
    }
}