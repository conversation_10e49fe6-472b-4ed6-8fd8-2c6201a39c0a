<?php
require_once 'db_connect.php';
require_admin();

// Fetch all workspaces
$stmt = $pdo->query("SELECT * FROM workspaces ORDER BY created_at DESC");
$workspaces = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle adding a new workspace
$add_success = '';
$add_error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['add_workspace'])) {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $add_error = "Invalid request. Please try again.";
    } else {
        $name = sanitize_input($_POST['name']);
        $location = sanitize_input($_POST['location']);
        $description = sanitize_input($_POST['description']);
        $noise_level = sanitize_input($_POST['noise_level']);
        $amenities = implode(',', array_map('sanitize_input', $_POST['amenities'] ?? []));
        $hourly_rate = filter_var($_POST['hourly_rate'], FILTER_VALIDATE_FLOAT);
        $half_day_rate = filter_var($_POST['half_day_rate'], FILTER_VALIDATE_FLOAT);
        $full_day_rate = filter_var($_POST['full_day_rate'], FILTER_VALIDATE_FLOAT);
        $monthly_rate = filter_var($_POST['monthly_rate'], FILTER_VALIDATE_FLOAT);

        // Validate required fields
        if (empty($name) || empty($location) || empty($description) || empty($noise_level) || empty($amenities)) {
            $add_error = "Please fill in all required fields.";
        } elseif (!in_array($noise_level, ['quiet', 'moderate', 'lively'])) {
            $add_error = "Invalid noise level.";
        } elseif ($hourly_rate === false || $half_day_rate === false || $full_day_rate === false || $monthly_rate === false) {
            $add_error = "Please enter valid rates.";
        } elseif ($hourly_rate <= 0 || $half_day_rate <= 0 || $full_day_rate <= 0 || $monthly_rate <= 0) {
            $add_error = "Rates must be greater than zero.";
        } else {
        // Handle image upload
        $image_path = null;
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $max_size = 5 * 1024 * 1024; // 5MB

            $file_type = mime_content_type($_FILES['image']['tmp_name']);
            $file_size = $_FILES['image']['size'];
            $file_tmp = $_FILES['image']['tmp_name'];
            $file_ext = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $file_name = 'workspace-' . time() . '.' . $file_ext;
            $upload_dir = 'uploads/';
            $image_path = $upload_dir . $file_name;

            // Validate file type and size
            if (!in_array($file_type, $allowed_types)) {
                $add_error = "Invalid image type. Only JPEG, PNG, and GIF are allowed.";
            } elseif ($file_size > $max_size) {
                $add_error = "Image size exceeds 5MB.";
            } else {
                // Move the uploaded file to the uploads directory
                if (!move_uploaded_file($file_tmp, $image_path)) {
                    $add_error = "Failed to upload image.";
                    $image_path = null;
                }
            }
        } else {
            $add_error = "Please upload an image.";
        }

        // If no errors, proceed to insert into database
        if (!$add_error) {
            try {
                $stmt = $pdo->prepare("INSERT INTO workspaces (name, location, description, noise_level, amenities, hourly_rate, half_day_rate, full_day_rate, monthly_rate, image_url) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute([$name, $location, $description, $noise_level, $amenities, $hourly_rate, $half_day_rate, $full_day_rate, $monthly_rate, $image_path]);
                $add_success = "Workspace added successfully!";
            } catch (PDOException $e) {
                $add_error = "Error: " . $e->getMessage();
            }
        }
    }
}

// Handle deleting a workspace
if (isset($_GET['delete']) && isset($_GET['csrf_token'])) {
    if (!verify_csrf_token($_GET['csrf_token'])) {
        $add_error = "Invalid request. Please try again.";
    } else {
        $workspace_id = filter_var($_GET['delete'], FILTER_VALIDATE_INT);
        if ($workspace_id === false) {
            $add_error = "Invalid workspace ID.";
        } else {
            try {
                // Fetch the workspace to get the image path
                $stmt = $pdo->prepare("SELECT image_url FROM workspaces WHERE id = ?");
                $stmt->execute([$workspace_id]);
                $workspace = $stmt->fetch();

                // Delete the image file if it exists
                if ($workspace && !empty($workspace['image_url']) && file_exists($workspace['image_url'])) {
                    unlink($workspace['image_url']);
                }

                // Delete the workspace from the database
                $stmt = $pdo->prepare("DELETE FROM workspaces WHERE id = ?");
                $stmt->execute([$workspace_id]);
                header("Location: admin_workspaces.php");
                exit;
            } catch (PDOException $e) {
                error_log("Workspace deletion error: " . $e->getMessage());
                $add_error = "Failed to delete workspace. Please try again.";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OfficeLink - Manage Workspaces</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">OfficeLink Admin</div>
            <nav>
                <div class="hamburger" onclick="toggleMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <ul id="nav-menu">
                    <li><a href="admin_dashboard.php">Dashboard</a></li>
                    <li><a href="admin_workspaces.php">Manage Workspaces</a></li>
                    <li><a href="admin_bookings.php">Manage Bookings</a></li>
                    <li><a href="admin_users.php">Manage Users</a></li>
                    <li><a href="admin_dashboard.php?logout=true">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Manage Workspaces Section -->
    <section id="admin-workspaces" class="admin-section">
        <div class="container">
            <h2>Manage Workspaces</h2>

            <!-- Add Workspace Form -->
            <div class="admin-card">
                <h3>Add New Workspace</h3>
                <?php if ($add_success): ?>
                    <p class="success"><?php echo $add_success; ?></p>
                <?php endif; ?>
                <?php if ($add_error): ?>
                    <p class="error"><?php echo $add_error; ?></p>
                <?php endif; ?>
                <form method="POST" class="admin-form" enctype="multipart/form-data">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <div class="form-group">
                        <label for="name">Workspace Name</label>
                        <input type="text" id="name" name="name" required>
                    </div>
                    <div class="form-group">
                        <label for="location">Location</label>
                        <input type="text" id="location" name="location" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="4" required></textarea>
                    </div>
                    <div class="form-group">
                        <label for="noise_level">Noise Level</label>
                        <select id="noise_level" name="noise_level" required>
                            <option value="quiet">Quiet</option>
                            <option value="moderate">Moderate</option>
                            <option value="lively">Lively</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Amenities</label>
                        <div class="checkbox-group">
                            <label><input type="checkbox" name="amenities[]" value="Wi-Fi"> Wi-Fi</label>
                            <label><input type="checkbox" name="amenities[]" value="ergonomic chairs"> Ergonomic Chairs</label>
                            <label><input type="checkbox" name="amenities[]" value="free snacks"> Free Snacks</label>
                            <label><input type="checkbox" name="amenities[]" value="projector"> Projector</label>
                            <label><input type="checkbox" name="amenities[]" value="coffee machine"> Coffee Machine</label>
                            <label><input type="checkbox" name="amenities[]" value="whiteboard"> Whiteboard</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="hourly_rate">Hourly Rate ($)</label>
                        <input type="number" id="hourly_rate" name="hourly_rate" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="half_day_rate">Half-Day Rate ($)</label>
                        <input type="number" id="half_day_rate" name="half_day_rate" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="full_day_rate">Full-Day Rate ($)</label>
                        <input type="number" id="full_day_rate" name="full_day_rate" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="monthly_rate">Monthly Rate ($)</label>
                        <input type="number" id="monthly_rate" name="monthly_rate" step="0.01" required>
                    </div>
                    <div class="form-group">
                        <label for="image">Workspace Image</label>
                        <input type="file" id="image" name="image" accept="image/*" required>
                    </div>
                    <button type="submit" name="add_workspace" class="btn-primary">Add Workspace</button>
                </form>
            </div>

            <!-- Workspaces List -->
            <div class="admin-card">
                <h3>All Workspaces</h3>
                <?php if (empty($workspaces)): ?>
                    <p>No workspaces found.</p>
                <?php else: ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Location</th>
                                <th>Noise Level</th>
                                <th>Hourly Rate</th>
                                <th>Image</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($workspaces as $workspace): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($workspace['name']); ?></td>
                                    <td><?php echo htmlspecialchars($workspace['location']); ?></td>
                                    <td><?php echo htmlspecialchars($workspace['noise_level']); ?></td>
                                    <td>$<?php echo number_format($workspace['hourly_rate'], 2); ?></td>
                                    <td><img src="<?php echo htmlspecialchars($workspace['image_url']); ?>" alt="<?php echo htmlspecialchars($workspace['name']); ?>" style="max-width: 100px;"></td>
                                    <td>
                                        <a href="admin_edit_workspace.php?id=<?php echo $workspace['id']; ?>" class="btn-edit">Edit</a>
                                        <a href="admin_workspaces.php?delete=<?php echo $workspace['id']; ?>&csrf_token=<?php echo generate_csrf_token(); ?>" class="btn-delete" onclick="return confirm('Are you sure you want to delete this workspace?');">Delete</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>OfficeLink</h4>
                    <p>Flexible workspaces for everyone.</p>
                </div>
                <div class="footer-section">
                    <h4>Links</h4>
                    <ul>
                        <li><a href="index.php#features">Features</a></li>
                        <li><a href="index.php#about">About</a></li>
                        <li><a href="signup.php">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +251 123 456 789</p>
                </div>
            </div>
            <p class="footer-bottom"> <?php echo date("Y"); ?> OfficeLink. All rights reserved.</p>
        </div>
    </footer>

    <script>
        function toggleMenu() {
            const menu = document.getElementById('nav-menu');
            const hamburger = document.querySelector('.hamburger');
            menu.classList.toggle('active');
            hamburger.classList.toggle('active');
        }
    </script>
</body>
</html>