<?php
// Chapa Payment Gateway Configuration

// Chapa API Configuration
define('CHAPA_SECRET_KEY', 'CHASECK_TEST-your-secret-key-here'); // Replace with your actual secret key
define('CHAPA_PUBLIC_KEY', 'CHAPUBK_TEST-your-public-key-here'); // Replace with your actual public key
define('CHAPA_BASE_URL', 'https://api.chapa.co/v1/');

// Test mode - set to false for production
define('CHAPA_TEST_MODE', true);

// Currency
define('CHAPA_CURRENCY', 'ETB');

// Chapa Payment Class
class ChapaPayment {
    private $secret_key;
    private $public_key;
    private $base_url;
    
    public function __construct() {
        $this->secret_key = CHAPA_SECRET_KEY;
        $this->public_key = CHAPA_PUBLIC_KEY;
        $this->base_url = CHAPA_BASE_URL;
    }
    
    /**
     * Initialize payment
     */
    public function initialize_payment($data) {
        $url = $this->base_url . 'transaction/initialize';
        
        $payload = [
            'amount' => $data['amount'],
            'currency' => CHAPA_CURRENCY,
            'email' => $data['email'],
            'first_name' => $data['first_name'],
            'last_name' => $data['last_name'],
            'phone_number' => $data['phone_number'],
            'tx_ref' => $data['tx_ref'],
            'callback_url' => $data['callback_url'],
            'return_url' => $data['return_url'],
            'description' => $data['description'],
            'meta' => $data['meta'] ?? []
        ];
        
        return $this->make_request($url, $payload);
    }
    
    /**
     * Verify payment
     */
    public function verify_payment($tx_ref) {
        $url = $this->base_url . 'transaction/verify/' . $tx_ref;
        
        return $this->make_request($url, null, 'GET');
    }
    
    /**
     * Get payment details
     */
    public function get_payment($payment_id) {
        $url = $this->base_url . 'transaction/' . $payment_id;
        
        return $this->make_request($url, null, 'GET');
    }
    
    /**
     * Make HTTP request to Chapa API
     */
    private function make_request($url, $data = null, $method = 'POST') {
        $headers = [
            'Authorization: Bearer ' . $this->secret_key,
            'Content-Type: application/json',
            'Accept: application/json'
        ];
        
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 30);
        
        if ($method === 'POST' && $data) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            return [
                'success' => false,
                'message' => 'cURL Error: ' . $error
            ];
        }
        
        $decoded_response = json_decode($response, true);
        
        if ($http_code >= 200 && $http_code < 300) {
            return [
                'success' => true,
                'data' => $decoded_response
            ];
        } else {
            return [
                'success' => false,
                'message' => $decoded_response['message'] ?? 'Payment request failed',
                'data' => $decoded_response
            ];
        }
    }
    
    /**
     * Generate transaction reference
     */
    public static function generate_tx_ref($prefix = 'OL') {
        return $prefix . '_' . time() . '_' . uniqid();
    }
    
    /**
     * Format amount for Chapa (remove decimals for ETB)
     */
    public static function format_amount($amount) {
        return number_format($amount, 2, '.', '');
    }
    
    /**
     * Validate webhook signature
     */
    public function validate_webhook($payload, $signature) {
        $computed_signature = hash_hmac('sha256', $payload, $this->secret_key);
        return hash_equals($computed_signature, $signature);
    }
}

// Payment status constants
define('PAYMENT_STATUS_PENDING', 'pending');
define('PAYMENT_STATUS_SUCCESS', 'success');
define('PAYMENT_STATUS_FAILED', 'failed');
define('PAYMENT_STATUS_CANCELLED', 'cancelled');

// Helper functions
function log_payment_event($event, $data = []) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event' => $event,
        'data' => $data,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_id' => $_SESSION['user_id'] ?? null
    ];
    
    $log_file = 'logs/payments.log';
    
    // Create logs directory if it doesn't exist
    if (!is_dir('logs')) {
        mkdir('logs', 0755, true);
    }
    
    file_put_contents($log_file, json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);
}

function get_site_url() {
    $protocol = isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on' ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'];
    $script_dir = dirname($_SERVER['SCRIPT_NAME']);
    return $protocol . '://' . $host . $script_dir;
}
