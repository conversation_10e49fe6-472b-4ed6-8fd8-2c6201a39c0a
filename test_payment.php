<?php
require_once 'db_connect.php';
require_once 'chapa_config.php';

echo "<h2>Payment System Test</h2>";

// Test 1: Check if payments table exists
echo "<h3>1. Testing Payments Table</h3>";
try {
    $stmt = $pdo->query("SHOW TABLES LIKE 'payments'");
    $table_exists = $stmt->fetch();
    if ($table_exists) {
        echo "✅ Payments table exists<br>";
    } else {
        echo "❌ Payments table does NOT exist<br>";
        echo "Creating payments table...<br>";
        
        $create_table_sql = "
        CREATE TABLE payments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            booking_id INT NOT NULL,
            user_id INT NOT NULL,
            tx_ref VARCHAR(255) UNIQUE NOT NULL,
            chapa_reference VARCHAR(255),
            amount DECIMAL(10,2) NOT NULL,
            currency VARCHAR(3) DEFAULT 'ETB',
            status ENUM('pending', 'success', 'failed', 'cancelled') DEFAULT 'pending',
            payment_method VARCHAR(50),
            chapa_response TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
        )";
        
        $pdo->exec($create_table_sql);
        echo "✅ Payments table created<br>";
    }
} catch (PDOException $e) {
    echo "❌ Error checking payments table: " . $e->getMessage() . "<br>";
}

// Test 2: Check if payment_status column exists in bookings
echo "<h3>2. Testing Bookings Table</h3>";
try {
    $stmt = $pdo->query("DESCRIBE bookings");
    $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
    if (in_array('payment_status', $columns)) {
        echo "✅ payment_status column exists in bookings table<br>";
    } else {
        echo "❌ payment_status column does NOT exist in bookings table<br>";
        echo "Adding payment_status column...<br>";
        $pdo->exec("ALTER TABLE bookings ADD COLUMN payment_status ENUM('unpaid', 'paid', 'refunded') DEFAULT 'unpaid'");
        echo "✅ payment_status column added<br>";
    }
} catch (PDOException $e) {
    echo "❌ Error checking bookings table: " . $e->getMessage() . "<br>";
}

// Test 3: Test Chapa demo mode
echo "<h3>3. Testing Chapa Demo Mode</h3>";
try {
    $chapa = new ChapaPayment();
    $test_data = [
        'amount' => '100.00',
        'currency' => 'ETB',
        'email' => '<EMAIL>',
        'first_name' => 'Test',
        'last_name' => 'User',
        'phone_number' => '+251123456789',
        'tx_ref' => 'TEST_' . time(),
        'callback_url' => 'http://localhost/callback',
        'return_url' => 'http://localhost/return',
        'description' => 'Test payment',
        'meta' => []
    ];
    
    $response = $chapa->initialize_payment($test_data);
    if ($response['success']) {
        echo "✅ Chapa demo mode working<br>";
        echo "Demo checkout URL: " . $response['data']['data']['checkout_url'] . "<br>";
    } else {
        echo "❌ Chapa demo mode failed: " . $response['message'] . "<br>";
    }
} catch (Exception $e) {
    echo "❌ Error testing Chapa: " . $e->getMessage() . "<br>";
}

// Test 4: Check recent bookings
echo "<h3>4. Recent Bookings</h3>";
try {
    $stmt = $pdo->query("SELECT id, workspace_name, total_cost, status FROM bookings ORDER BY created_at DESC LIMIT 5");
    $bookings = $stmt->fetchAll();
    if ($bookings) {
        echo "<table border='1'>";
        echo "<tr><th>ID</th><th>Workspace</th><th>Cost</th><th>Status</th><th>Action</th></tr>";
        foreach ($bookings as $booking) {
            echo "<tr>";
            echo "<td>" . $booking['id'] . "</td>";
            echo "<td>" . htmlspecialchars($booking['workspace_name']) . "</td>";
            echo "<td>" . $booking['total_cost'] . " ETB</td>";
            echo "<td>" . $booking['status'] . "</td>";
            echo "<td><a href='payment.php?booking_id=" . $booking['id'] . "'>Test Payment</a></td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "No bookings found<br>";
    }
} catch (PDOException $e) {
    echo "❌ Error fetching bookings: " . $e->getMessage() . "<br>";
}

echo "<br><a href='dashboard.php'>← Back to Dashboard</a>";
?>
