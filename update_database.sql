-- Update existing database for payment integration
-- Run this script in your MySQL database

USE officelink;

-- Add payment_status column to bookings table
ALTER TABLE bookings ADD COLUMN payment_status ENUM('unpaid', 'paid', 'refunded') DEFAULT 'unpaid';

-- Add is_active column to workspaces table  
ALTER TABLE workspaces ADD COLUMN is_active TINYINT(1) DEFAULT 1;

-- Update all existing bookings to have unpaid status
UPDATE bookings SET payment_status = 'unpaid' WHERE payment_status IS NULL;

-- Update all existing workspaces to be active
UPDATE workspaces SET is_active = 1 WHERE is_active IS NULL;

-- Create payments table
CREATE TABLE IF NOT EXISTS payments (
    id INT AUTO_INCREMENT PRIMARY KEY,
    booking_id INT NOT NULL,
    user_id INT NOT NULL,
    tx_ref VARCHAR(255) UNIQUE NOT NULL,
    chapa_reference VARCHAR(255),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) DEFAULT 'ETB',
    status ENUM('pending', 'success', 'failed', 'cancelled') DEFAULT 'pending',
    payment_method VARCHAR(50),
    chapa_response TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (booking_id) REFERENCES bookings(id) ON DELETE CASCADE,
    FOREIGN KEY (user_id) REFERENCES users(id) ON DELETE CASCADE,
    INDEX idx_tx_ref (tx_ref),
    INDEX idx_booking_id (booking_id),
    INDEX idx_status (status)
);

-- Show tables to verify
SHOW TABLES;

-- Show bookings table structure
DESCRIBE bookings;

-- Show workspaces table structure  
DESCRIBE workspaces;

-- Show payments table structure
DESCRIBE payments;
