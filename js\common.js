// Common JavaScript functions for OfficeLink

// Toggle mobile menu
function toggleMenu() {
    const menu = document.getElementById('nav-menu');
    const hamburger = document.querySelector('.hamburger');
    menu.classList.toggle('active');
    hamburger.classList.toggle('active');
}

// Toggle time inputs for booking form
function toggleTimeInputs() {
    const bookingType = document.getElementById('booking_type');
    if (!bookingType) return;
    
    const timeInputs = document.querySelectorAll('.time-inputs');
    if (bookingType.value === 'hourly') {
        timeInputs.forEach(input => {
            input.style.display = 'block';
            const inputField = input.querySelector('input');
            if (inputField) inputField.setAttribute('required', 'required');
        });
    } else {
        timeInputs.forEach(input => {
            input.style.display = 'none';
            const inputField = input.querySelector('input');
            if (inputField) inputField.removeAttribute('required');
        });
    }
}

// Form validation helpers
function validateEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

function validatePassword(password) {
    return password.length >= 6;
}

function validatePasswordMatch(password, confirmPassword) {
    return password === confirmPassword;
}

// Client-side form validation for signup
function validateSignupForm() {
    const form = document.querySelector('form[method="POST"]');
    if (!form) return;

    form.addEventListener('submit', function(e) {
        const fullName = document.getElementById('full_name');
        const email = document.getElementById('email');
        const password = document.getElementById('password');
        const confirmPassword = document.getElementById('confirm_password');
        
        let isValid = true;
        let errorMessage = '';

        // Clear previous error styles
        [fullName, email, password, confirmPassword].forEach(field => {
            if (field) field.style.borderColor = '';
        });

        // Validate full name
        if (fullName && fullName.value.trim().length < 2) {
            fullName.style.borderColor = '#e74c3c';
            errorMessage = 'Full name must be at least 2 characters long.';
            isValid = false;
        }

        // Validate email
        if (email && !validateEmail(email.value)) {
            email.style.borderColor = '#e74c3c';
            errorMessage = 'Please enter a valid email address.';
            isValid = false;
        }

        // Validate password
        if (password && !validatePassword(password.value)) {
            password.style.borderColor = '#e74c3c';
            errorMessage = 'Password must be at least 6 characters long.';
            isValid = false;
        }

        // Validate password match
        if (password && confirmPassword && !validatePasswordMatch(password.value, confirmPassword.value)) {
            confirmPassword.style.borderColor = '#e74c3c';
            errorMessage = 'Passwords do not match.';
            isValid = false;
        }

        if (!isValid) {
            e.preventDefault();
            showMessage(errorMessage, 'error');
        }
    });
}

// Show message to user
function showMessage(message, type = 'info') {
    // Remove existing messages
    const existingMessages = document.querySelectorAll('.js-message');
    existingMessages.forEach(msg => msg.remove());

    // Create new message element
    const messageDiv = document.createElement('div');
    messageDiv.className = `message js-message ${type}`;
    messageDiv.textContent = message;

    // Insert message at the top of the main content
    const container = document.querySelector('.container');
    if (container) {
        container.insertBefore(messageDiv, container.firstChild);
        
        // Auto-hide after 5 seconds
        setTimeout(() => {
            messageDiv.remove();
        }, 5000);
    }
}

// Confirm deletion
function confirmDelete(message = 'Are you sure you want to delete this item?') {
    return confirm(message);
}

// Initialize common functionality when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    // Initialize signup form validation if on signup page
    if (document.getElementById('full_name')) {
        validateSignupForm();
    }

    // Initialize booking form functionality if on booking page
    if (document.getElementById('booking_type')) {
        toggleTimeInputs(); // Set initial state
    }

    // Add click handlers for delete buttons
    const deleteButtons = document.querySelectorAll('.btn-delete');
    deleteButtons.forEach(button => {
        button.addEventListener('click', function(e) {
            if (!confirmDelete()) {
                e.preventDefault();
            }
        });
    });

    // Auto-hide messages after 5 seconds
    const messages = document.querySelectorAll('.message:not(.js-message)');
    messages.forEach(message => {
        setTimeout(() => {
            message.style.opacity = '0';
            setTimeout(() => message.remove(), 300);
        }, 5000);
    });
});

// File upload preview (for admin forms)
function previewImage(input) {
    if (input.files && input.files[0]) {
        const reader = new FileReader();
        reader.onload = function(e) {
            const preview = document.getElementById('image-preview');
            if (preview) {
                preview.src = e.target.result;
                preview.style.display = 'block';
            }
        };
        reader.readAsDataURL(input.files[0]);
    }
}

// Validate file upload
function validateFileUpload(input) {
    const file = input.files[0];
    if (!file) return true;

    const allowedTypes = ['image/jpeg', 'image/png', 'image/gif'];
    const maxSize = 5 * 1024 * 1024; // 5MB

    if (!allowedTypes.includes(file.type)) {
        showMessage('Invalid file type. Only JPEG, PNG, and GIF are allowed.', 'error');
        input.value = '';
        return false;
    }

    if (file.size > maxSize) {
        showMessage('File size exceeds 5MB limit.', 'error');
        input.value = '';
        return false;
    }

    return true;
}
