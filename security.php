<?php
// OfficeLink Security Configuration and Functions

// Security headers
function set_security_headers() {
    // Prevent XSS attacks
    header('X-XSS-Protection: 1; mode=block');
    
    // Prevent MIME type sniffing
    header('X-Content-Type-Options: nosniff');
    
    // Prevent clickjacking
    header('X-Frame-Options: SAMEORIGIN');
    
    // Referrer Policy
    header('Referrer-Policy: strict-origin-when-cross-origin');
    
    // Remove server information
    header_remove('X-Powered-By');
    header_remove('Server');
}

// Rate limiting for login attempts
function check_rate_limit($identifier, $max_attempts = 5, $time_window = 900) { // 15 minutes
    $cache_file = sys_get_temp_dir() . '/officelink_rate_limit_' . md5($identifier);
    
    if (file_exists($cache_file)) {
        $data = json_decode(file_get_contents($cache_file), true);
        $current_time = time();
        
        // Clean old attempts
        $data['attempts'] = array_filter($data['attempts'], function($timestamp) use ($current_time, $time_window) {
            return ($current_time - $timestamp) < $time_window;
        });
        
        if (count($data['attempts']) >= $max_attempts) {
            return false; // Rate limit exceeded
        }
    } else {
        $data = ['attempts' => []];
    }
    
    // Record this attempt
    $data['attempts'][] = time();
    file_put_contents($cache_file, json_encode($data));
    
    return true; // Within rate limit
}

// Log security events
function log_security_event($event, $details = []) {
    $log_entry = [
        'timestamp' => date('Y-m-d H:i:s'),
        'event' => $event,
        'ip' => $_SERVER['REMOTE_ADDR'] ?? 'unknown',
        'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'unknown',
        'details' => $details
    ];
    
    $log_file = 'logs/security.log';
    
    // Create logs directory if it doesn't exist
    if (!is_dir('logs')) {
        mkdir('logs', 0755, true);
    }
    
    file_put_contents($log_file, json_encode($log_entry) . "\n", FILE_APPEND | LOCK_EX);
}

// Validate file upload security
function validate_upload_security($file) {
    $errors = [];
    
    // Check if file was uploaded via HTTP POST
    if (!is_uploaded_file($file['tmp_name'])) {
        $errors[] = 'File was not uploaded via HTTP POST';
        return $errors;
    }
    
    // Check file size
    $max_size = 5 * 1024 * 1024; // 5MB
    if ($file['size'] > $max_size) {
        $errors[] = 'File size exceeds maximum allowed size';
    }
    
    // Check MIME type
    $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
    $finfo = finfo_open(FILEINFO_MIME_TYPE);
    $mime_type = finfo_file($finfo, $file['tmp_name']);
    finfo_close($finfo);
    
    if (!in_array($mime_type, $allowed_types)) {
        $errors[] = 'Invalid file type';
    }
    
    // Check file extension
    $allowed_extensions = ['jpg', 'jpeg', 'png', 'gif'];
    $file_extension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
    
    if (!in_array($file_extension, $allowed_extensions)) {
        $errors[] = 'Invalid file extension';
    }
    
    // Check if file is actually an image
    $image_info = getimagesize($file['tmp_name']);
    if ($image_info === false) {
        $errors[] = 'File is not a valid image';
    }
    
    // Check for embedded PHP code (basic check)
    $file_content = file_get_contents($file['tmp_name']);
    if (strpos($file_content, '<?php') !== false || strpos($file_content, '<?=') !== false) {
        $errors[] = 'File contains potentially dangerous content';
    }
    
    return $errors;
}

// Generate secure random token
function generate_secure_token($length = 32) {
    return bin2hex(random_bytes($length));
}

// Secure session configuration
function configure_secure_session() {
    // Set secure session parameters
    ini_set('session.cookie_httponly', 1);
    ini_set('session.use_only_cookies', 1);
    ini_set('session.cookie_secure', isset($_SERVER['HTTPS']));
    ini_set('session.cookie_samesite', 'Strict');
    
    // Regenerate session ID periodically
    if (!isset($_SESSION['last_regeneration'])) {
        $_SESSION['last_regeneration'] = time();
    } elseif (time() - $_SESSION['last_regeneration'] > 300) { // 5 minutes
        session_regenerate_id(true);
        $_SESSION['last_regeneration'] = time();
    }
}

// Input validation helpers
function validate_integer($value, $min = null, $max = null) {
    $int_value = filter_var($value, FILTER_VALIDATE_INT);
    if ($int_value === false) {
        return false;
    }
    
    if ($min !== null && $int_value < $min) {
        return false;
    }
    
    if ($max !== null && $int_value > $max) {
        return false;
    }
    
    return $int_value;
}

function validate_float($value, $min = null, $max = null) {
    $float_value = filter_var($value, FILTER_VALIDATE_FLOAT);
    if ($float_value === false) {
        return false;
    }
    
    if ($min !== null && $float_value < $min) {
        return false;
    }
    
    if ($max !== null && $float_value > $max) {
        return false;
    }
    
    return $float_value;
}

function validate_date($date, $format = 'Y-m-d') {
    $d = DateTime::createFromFormat($format, $date);
    return $d && $d->format($format) === $date;
}

// Check for suspicious activity
function detect_suspicious_activity() {
    $suspicious_patterns = [
        'union.*select',
        'script.*alert',
        'javascript:',
        'vbscript:',
        'onload=',
        'onerror=',
        'eval\(',
        'base64_decode',
        'file_get_contents',
        'system\(',
        'exec\(',
        'shell_exec',
        'passthru',
        'proc_open'
    ];
    
    $input_sources = [
        $_GET,
        $_POST,
        $_COOKIE,
        $_SERVER['HTTP_USER_AGENT'] ?? '',
        $_SERVER['HTTP_REFERER'] ?? ''
    ];
    
    foreach ($input_sources as $source) {
        if (is_array($source)) {
            foreach ($source as $value) {
                if (is_string($value)) {
                    foreach ($suspicious_patterns as $pattern) {
                        if (preg_match('/' . $pattern . '/i', $value)) {
                            log_security_event('suspicious_activity', [
                                'pattern' => $pattern,
                                'value' => substr($value, 0, 100)
                            ]);
                            return true;
                        }
                    }
                }
            }
        } elseif (is_string($source)) {
            foreach ($suspicious_patterns as $pattern) {
                if (preg_match('/' . $pattern . '/i', $source)) {
                    log_security_event('suspicious_activity', [
                        'pattern' => $pattern,
                        'value' => substr($source, 0, 100)
                    ]);
                    return true;
                }
            }
        }
    }
    
    return false;
}

// Initialize security measures
function init_security() {
    set_security_headers();
    configure_secure_session();
    
    // Check for suspicious activity
    if (detect_suspicious_activity()) {
        // Log and potentially block the request
        http_response_code(403);
        die('Access denied');
    }
}

// Call security initialization
init_security();
