# Chapa Payment Integration Setup Guide

## Overview
This guide will help you set up Chapa payment integration for the OfficeLink booking system.

## Prerequisites
1. Chapa merchant account
2. Chapa API keys (test and live)
3. SSL certificate for production (required by Chapa)

## Step 1: Get Chapa API Keys

### Test Environment
1. Visit [Chapa Dashboard](https://dashboard.chapa.co)
2. Sign up or log in to your account
3. Navigate to API Keys section
4. Copy your test keys:
   - Test Secret Key (starts with `CHASECK_TEST-`)
   - Test Public Key (starts with `CHAPUBK_TEST-`)

### Production Environment
1. Complete Chapa verification process
2. Get your live API keys:
   - Live Secret Key (starts with `CHASECK-`)
   - Live Public Key (starts with `CHAPUBK-`)

## Step 2: Configure API Keys

1. Open `chapa_config.php`
2. Replace the placeholder keys with your actual keys:

```php
// For testing
define('CHAPA_SECRET_KEY', 'CHASECK_TEST-your-actual-test-secret-key');
define('CHAPA_PUBLIC_KEY', 'CHAPUBK_TEST-your-actual-test-public-key');
define('CHAPA_TEST_MODE', true);

// For production
define('CHAPA_SECRET_KEY', 'CHASECK-your-actual-live-secret-key');
define('CHAPA_PUBLIC_KEY', 'CHAPUBK-your-actual-live-public-key');
define('CHAPA_TEST_MODE', false);
```

## Step 3: Database Setup

Run the SQL script to add payment tables:

```sql
-- Run this in your MySQL database
source add_payment_columns.sql;
```

Or manually execute:
```sql
ALTER TABLE bookings ADD COLUMN payment_status ENUM('unpaid', 'paid', 'refunded') DEFAULT 'unpaid';
```

## Step 4: Webhook Configuration

### Set up webhook URL in Chapa Dashboard:
- Webhook URL: `https://yourdomain.com/payment_callback.php`
- Events to subscribe to:
  - `charge.success`
  - `charge.failed`
  - `charge.cancelled`

### Test webhook locally (for development):
1. Use ngrok or similar tool to expose your local server
2. Set webhook URL to: `https://your-ngrok-url.ngrok.io/payment_callback.php`

## Step 5: SSL Certificate (Production Only)

Chapa requires HTTPS for production. Ensure your server has:
1. Valid SSL certificate
2. HTTPS enabled
3. HTTP to HTTPS redirect configured

## Step 6: Testing

### Test Payment Flow:
1. Create a booking
2. Click "Pay Now" button
3. You'll be redirected to Chapa checkout
4. Use test card details:
   - Card Number: `****************`
   - Expiry: Any future date
   - CVV: Any 3 digits

### Test Cards for Different Scenarios:
- **Successful Payment**: `****************`
- **Failed Payment**: `****************`
- **Insufficient Funds**: `****************`

## Step 7: Go Live

### Before going live:
1. ✅ Complete Chapa merchant verification
2. ✅ Update API keys to live keys
3. ✅ Set `CHAPA_TEST_MODE` to `false`
4. ✅ Ensure SSL certificate is installed
5. ✅ Test webhook with live environment
6. ✅ Update webhook URL to production URL

### Production checklist:
```php
// In chapa_config.php
define('CHAPA_SECRET_KEY', 'CHASECK-your-live-secret-key');
define('CHAPA_PUBLIC_KEY', 'CHAPUBK-your-live-public-key');
define('CHAPA_TEST_MODE', false);
```

## Payment Flow

### User Journey:
1. User searches and selects workspace
2. User fills booking form and submits
3. System creates booking with status "pending" and payment_status "unpaid"
4. User is redirected to payment page
5. User clicks "Pay Now" and is redirected to Chapa
6. User completes payment on Chapa
7. Chapa sends webhook to your callback URL
8. System updates booking and payment status
9. User is redirected to success page

### Payment Status Flow:
- **Booking Created**: `status = 'pending'`, `payment_status = 'unpaid'`
- **Payment Successful**: `status = 'confirmed'`, `payment_status = 'paid'`
- **Payment Failed**: `status = 'cancelled'`, `payment_status = 'unpaid'`

## Troubleshooting

### Common Issues:

#### 1. "Payment initialization failed"
- Check API keys are correct
- Verify internet connection
- Check Chapa service status

#### 2. "Webhook not receiving callbacks"
- Verify webhook URL is accessible
- Check webhook URL in Chapa dashboard
- Ensure server can receive POST requests

#### 3. "SSL Certificate errors"
- Install valid SSL certificate
- Verify HTTPS is working
- Check certificate chain

#### 4. "Database errors"
- Run `add_payment_columns.sql`
- Check database permissions
- Verify table structure

### Log Files:
- Payment events: `logs/payments.log`
- Application errors: `logs/application_errors.log`
- Security events: `logs/security.log`

### Debug Mode:
Enable debug logging by adding to `chapa_config.php`:
```php
define('CHAPA_DEBUG', true);
```

## Security Considerations

1. **Never expose secret keys** in client-side code
2. **Always validate webhook signatures** (if provided by Chapa)
3. **Use HTTPS** for all payment-related pages
4. **Log all payment events** for audit trail
5. **Validate payment amounts** before processing
6. **Implement rate limiting** on payment endpoints

## Support

### Chapa Support:
- Email: <EMAIL>
- Documentation: https://developer.chapa.co
- Dashboard: https://dashboard.chapa.co

### Integration Support:
- Check logs in `logs/` directory
- Review webhook responses
- Test with Chapa's test environment first

## File Structure

```
/
├── chapa_config.php          # Chapa configuration and API class
├── payment.php               # Payment page
├── payment_callback.php      # Webhook handler
├── payment_success.php       # Payment result page
├── add_payment_columns.sql   # Database setup
└── logs/
    ├── payments.log          # Payment events
    └── application_errors.log # Error logs
```

## Next Steps

1. Set up your Chapa merchant account
2. Configure API keys
3. Run database migrations
4. Test payment flow
5. Set up webhooks
6. Go live!

Remember to always test thoroughly in the test environment before going live with real payments.
