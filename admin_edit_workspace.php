<?php
session_start();
require_once 'db_connect.php';

// Check if the user is logged in and is an admin
if (!isset($_SESSION['user_id']) || !$_SESSION['is_admin']) {
    header("Location: login.php");
    exit;
}

// Check if workspace_id is provided
if (!isset($_GET['id'])) {
    header("Location: admin_workspaces.php");
    exit;
}

$workspace_id = $_GET['id'];

// Fetch workspace details
$stmt = $pdo->prepare("SELECT * FROM workspaces WHERE id = ?");
$stmt->execute([$workspace_id]);
$workspace = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$workspace) {
    header("Location: admin_workspaces.php");
    exit;
}

// Handle updating the workspace
$update_success = '';
$update_error = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['update_workspace'])) {
    $name = trim($_POST['name']);
    $location = trim($_POST['location']);
    $description = trim($_POST['description']);
    $noise_level = $_POST['noise_level'];
    $amenities = implode(',', $_POST['amenities'] ?? []);
    $hourly_rate = $_POST['hourly_rate'];
    $half_day_rate = $_POST['half_day_rate'];
    $full_day_rate = $_POST['full_day_rate'];
    $monthly_rate = $_POST['monthly_rate'];

    // Validate required fields (excluding image for now)
    if (empty($name) || empty($location) || empty($description) || empty($noise_level) || empty($amenities) || empty($hourly_rate) || empty($half_day_rate) || empty($full_day_rate) || empty($monthly_rate)) {
        $update_error = "Please fill in all fields.";
    } else {
        // Handle image upload (optional when editing)
        $image_path = $workspace['image_url']; // Keep the existing image by default
        if (isset($_FILES['image']) && $_FILES['image']['error'] === UPLOAD_ERR_OK) {
            $allowed_types = ['image/jpeg', 'image/png', 'image/gif'];
            $max_size = 5 * 1024 * 1024; // 5MB

            $file_type = mime_content_type($_FILES['image']['tmp_name']);
            $file_size = $_FILES['image']['size'];
            $file_tmp = $_FILES['image']['tmp_name'];
            $file_ext = strtolower(pathinfo($_FILES['image']['name'], PATHINFO_EXTENSION));
            $file_name = 'workspace-' . time() . '.' . $file_ext;
            $upload_dir = 'uploads/';
            $image_path = $upload_dir . $file_name;

            // Validate file type and size
            if (!in_array($file_type, $allowed_types)) {
                $update_error = "Invalid image type. Only JPEG, PNG, and GIF are allowed.";
            } elseif ($file_size > $max_size) {
                $update_error = "Image size exceeds 5MB.";
            } else {
                // Delete the old image if it exists
                if (file_exists($workspace['image_url'])) {
                    unlink($workspace['image_url']);
                }
                // Move the new uploaded file
                if (!move_uploaded_file($file_tmp, $image_path)) {
                    $update_error = "Failed to upload image.";
                    $image_path = $workspace['image_url']; // Revert to old image on failure
                }
            }
        }

        // If no errors, proceed to update the database
        if (!$update_error) {
            try {
                $stmt = $pdo->prepare("UPDATE workspaces SET name = ?, location = ?, description = ?, noise_level = ?, amenities = ?, hourly_rate = ?, half_day_rate = ?, full_day_rate = ?, monthly_rate = ?, image_url = ? WHERE id = ?");
                $stmt->execute([$name, $location, $description, $noise_level, $amenities, $hourly_rate, $half_day_rate, $full_day_rate, $monthly_rate, $image_path, $workspace_id]);
                $update_success = "Workspace updated successfully!";
            } catch (PDOException $e) {
                $update_error = "Error: " . $e->getMessage();
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OfficeLink - Edit Workspace</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">OfficeLink Admin</div>
            <nav>
                <div class="hamburger" onclick="toggleMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <ul id="nav-menu">
                    <li><a href="admin_dashboard.php">Dashboard</a></li>
                    <li><a href="admin_workspaces.php">Manage Workspaces</a></li>
                    <li><a href="admin_bookings.php">Manage Bookings</a></li>
                    <li><a href="admin_users.php">Manage Users</a></li>
                    <li><a href="admin_dashboard.php?logout=true">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Edit Workspace Section -->
    <section id="admin-edit-workspace" class="admin-section">
        <div class="container">
            <h2>Edit Workspace</h2>
            <div class="admin-card">
                <h3>Update Workspace Details</h3>
                <?php if ($update_success): ?>
                    <p class="success"><?php echo $update_success; ?></p>
                <?php endif; ?>
                <?php if ($update_error): ?>
                    <p class="error"><?php echo $update_error; ?></p>
                <?php endif; ?>
                <form method="POST" class="admin-form" enctype="multipart/form-data">
                    <div class="form-group">
                        <label for="name">Workspace Name</label>
                        <input type="text" id="name" name="name" value="<?php echo htmlspecialchars($workspace['name']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="location">Location</label>
                        <input type="text" id="location" name="location" value="<?php echo htmlspecialchars($workspace['location']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="description">Description</label>
                        <textarea id="description" name="description" rows="4" required><?php echo htmlspecialchars($workspace['description']); ?></textarea>
                    </div>
                    <div class="form-group">
                        <label for="noise_level">Noise Level</label>
                        <select id="noise_level" name="noise_level" required>
                            <option value="quiet" <?php echo $workspace['noise_level'] === 'quiet' ? 'selected' : ''; ?>>Quiet</option>
                            <option value="moderate" <?php echo $workspace['noise_level'] === 'moderate' ? 'selected' : ''; ?>>Moderate</option>
                            <option value="lively" <?php echo $workspace['noise_level'] === 'lively' ? 'selected' : ''; ?>>Lively</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>Amenities</label>
                        <div class="checkbox-group">
                            <?php $current_amenities = explode(',', $workspace['amenities']); ?>
                            <label><input type="checkbox" name="amenities[]" value="Wi-Fi" <?php echo in_array('Wi-Fi', $current_amenities) ? 'checked' : ''; ?>> Wi-Fi</label>
                            <label><input type="checkbox" name="amenities[]" value="ergonomic chairs" <?php echo in_array('ergonomic chairs', $current_amenities) ? 'checked' : ''; ?>> Ergonomic Chairs</label>
                            <label><input type="checkbox" name="amenities[]" value="free snacks" <?php echo in_array('free snacks', $current_amenities) ? 'checked' : ''; ?>> Free Snacks</label>
                            <label><input type="checkbox" name="amenities[]" value="projector" <?php echo in_array('projector', $current_amenities) ? 'checked' : ''; ?>> Projector</label>
                            <label><input type="checkbox" name="amenities[]" value="coffee machine" <?php echo in_array('coffee machine', $current_amenities) ? 'checked' : ''; ?>> Coffee Machine</label>
                            <label><input type="checkbox" name="amenities[]" value="whiteboard" <?php echo in_array('whiteboard', $current_amenities) ? 'checked' : ''; ?>> Whiteboard</label>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="hourly_rate">Hourly Rate ($)</label>
                        <input type="number" id="hourly_rate" name="hourly_rate" step="0.01" value="<?php echo htmlspecialchars($workspace['hourly_rate']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="half_day_rate">Half-Day Rate ($)</label>
                        <input type="number" id="half_day_rate" name="half_day_rate" step="0.01" value="<?php echo htmlspecialchars($workspace['half_day_rate']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="full_day_rate">Full-Day Rate ($)</label>
                        <input type="number" id="full_day_rate" name="full_day_rate" step="0.01" value="<?php echo htmlspecialchars($workspace['full_day_rate']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label for="monthly_rate">Monthly Rate ($)</label>
                        <input type="number" id="monthly_rate" name="monthly_rate" step="0.01" value="<?php echo htmlspecialchars($workspace['monthly_rate']); ?>" required>
                    </div>
                    <div class="form-group">
                        <label>Current Image</label>
                        <img src="<?php echo htmlspecialchars($workspace['image_url']); ?>" alt="<?php echo htmlspecialchars($workspace['name']); ?>" style="max-width: 200px; display: block; margin-bottom: 10px;">
                    </div>
                    <div class="form-group">
                        <label for="image">Upload New Image (Optional)</label>
                        <input type="file" id="image" name="image" accept="image/*">
                    </div>
                    <button type="submit" name="update_workspace" class="btn-primary">Update Workspace</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>OfficeLink</h4>
                    <p>Flexible workspaces for everyone.</p>
                </div>
                <div class="footer-section">
                    <h4>Links</h4>
                    <ul>
                        <li><a href="index.php#features">Features</a></li>
                        <li><a href="index.php#about">About</a></li>
                        <li><a href="signup.php">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +251 123 456 78</p>
                </div>
            </div>
            <p class="footer-bottom"> <?php echo date("Y"); ?> OfficeLink. All rights reserved.</p>
        </div>
    </footer>

    <script>
        function toggleMenu() {
            const menu = document.getElementById('nav-menu');
            const hamburger = document.querySelector('.hamburger');
            menu.classList.toggle('active');
            hamburger.classList.toggle('active');
        }
    </script>
</body>
</html>