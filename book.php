<?php
require_once 'db_connect.php';
require_login();

$user_id = $_SESSION['user_id'];

// Check if workspace_id is provided
if (!isset($_GET['workspace_id'])) {
    header("Location: search.php");
    exit;
}

$workspace_id = $_GET['workspace_id'];

// Fetch workspace details
$stmt = $pdo->prepare("SELECT * FROM workspaces WHERE id = ?");
$stmt->execute([$workspace_id]);
$workspace = $stmt->fetch(PDO::FETCH_ASSOC);

if (!$workspace) {
    header("Location: search.php");
    exit;
}

// Handle booking submission
$booking_error = '';
$booking_success = '';
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['book'])) {
    // Verify CSRF token
    if (!isset($_POST['csrf_token']) || !verify_csrf_token($_POST['csrf_token'])) {
        $booking_error = "Invalid request. Please try again.";
    } else {
        $booking_type = sanitize_input($_POST['booking_type']);
        $booking_date = sanitize_input($_POST['booking_date']);
        $start_time = isset($_POST['start_time']) ? sanitize_input($_POST['start_time']) : null;
        $end_time = isset($_POST['end_time']) ? sanitize_input($_POST['end_time']) : null;

        // Validate inputs
        if (empty($booking_type) || empty($booking_date)) {
            $booking_error = "Please fill in all required fields.";
        } elseif (!in_array($booking_type, ['hourly', 'half-day', 'full-day', 'monthly'])) {
            $booking_error = "Invalid booking type.";
        } elseif (strtotime($booking_date) < strtotime(date('Y-m-d'))) {
            $booking_error = "Booking date cannot be in the past.";
        } else {
        // Calculate cost based on booking type
        $base_cost = 0;
        $start_datetime = null;
        $end_datetime = null;

        if ($booking_type === 'hourly') {
            if (!$start_time || !$end_time) {
                $booking_error = "Please specify start and end times for hourly bookings.";
            } else {
                $start_datetime = "$booking_date $start_time";
                $end_datetime = "$booking_date $end_time";
                $hours = (strtotime($end_datetime) - strtotime($start_datetime)) / 3600;
                if ($hours <= 0) {
                    $booking_error = "End time must be after start time.";
                } else {
                    $base_cost = $workspace['hourly_rate'] * $hours;
                }
            }
        } elseif ($booking_type === 'half-day') {
            $base_cost = $workspace['half_day_rate'];
            $start_datetime = "$booking_date 09:00:00"; // Assume half-day is 9 AM to 1 PM
            $end_datetime = "$booking_date 13:00:00";
        } elseif ($booking_type === 'full-day') {
            $base_cost = $workspace['full_day_rate'];
            $start_datetime = "$booking_date 09:00:00"; // Assume full-day is 9 AM to 5 PM
            $end_datetime = "$booking_date 17:00:00";
        } elseif ($booking_type === 'monthly') {
            $base_cost = $workspace['monthly_rate'];
            $start_datetime = "$booking_date 00:00:00";
            $end_datetime = date('Y-m-d H:i:s', strtotime("$booking_date +1 month"));
        }

        if (!$booking_error) {
            // Calculate total cost (10% tax + $2 service fee)
            $tax = $base_cost * 0.10;
            $service_fee = 2.00;
            $total_cost = $base_cost + $tax + $service_fee;

            // Save the booking
            try {
                // Try to insert with payment_status column (after database update)
                try {
                    $stmt = $pdo->prepare("INSERT INTO bookings (user_id, workspace_id, workspace_name, location, booking_date, booking_type, start_time, end_time, total_cost, status, payment_status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $user_id,
                        $workspace_id,
                        $workspace['name'],
                        $workspace['location'],
                        $booking_date,
                        $booking_type,
                        $start_datetime,
                        $end_datetime,
                        $total_cost,
                        'pending',
                        'unpaid'
                    ]);
                } catch (PDOException $e) {
                    // Fallback for old database structure (before update)
                    $stmt = $pdo->prepare("INSERT INTO bookings (user_id, workspace_id, workspace_name, location, booking_date, booking_type, start_time, end_time, total_cost, status) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                    $stmt->execute([
                        $user_id,
                        $workspace_id,
                        $workspace['name'],
                        $workspace['location'],
                        $booking_date,
                        $booking_type,
                        $start_datetime,
                        $end_datetime,
                        $total_cost,
                        'pending'
                    ]);
                }

                $booking_id = $pdo->lastInsertId();

                // Redirect to payment page
                header("Location: payment.php?booking_id=" . $booking_id);
                exit;
            } catch (PDOException $e) {
                error_log("Booking error: " . $e->getMessage());
                $booking_error = "Booking failed. Please try again.";
            }
        }
    }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OfficeLink - Book Workspace</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">OfficeLink</div>
            <nav>
                <div class="hamburger" onclick="toggleMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <ul id="nav-menu">
                    <li><a href="index.php">Home</a></li>
                    <li><a href="index.php#features">Features</a></li>
                    <li><a href="index.php#about">About</a></li>
                    <li><a href="search.php">Search</a></li>
                    <li><a href="dashboard.php">Dashboard</a></li>
                    <li><a href="dashboard.php?logout=true">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Booking Section -->
    <section id="book" class="book-section">
        <div class="container">
            <h2>Book Your Workspace</h2>
            <div class="workspace-card">
                <img src="<?php echo htmlspecialchars($workspace['image_url']); ?>" alt="<?php echo htmlspecialchars($workspace['name']); ?>">
                <div class="workspace-details">
                    <h3><?php echo htmlspecialchars($workspace['name']); ?></h3>
                    <p><strong>Location:</strong> <?php echo htmlspecialchars($workspace['location']); ?></p>
                    <p><strong>Pricing:</strong></p>
                    <ul>
                        <li>Hourly: $<?php echo number_format($workspace['hourly_rate'], 2); ?></li>
                        <li>Half-Day: $<?php echo number_format($workspace['half_day_rate'], 2); ?></li>
                        <li>Full-Day: $<?php echo number_format($workspace['full_day_rate'], 2); ?></li>
                        <li>Monthly: $<?php echo number_format($workspace['monthly_rate'], 2); ?></li>
                    </ul>
                </div>
            </div>

            <!-- Booking Form -->
            <div class="booking-form-container">
                <h3>Booking Details</h3>
                <?php if ($booking_success): ?>
                    <p class="success"><?php echo $booking_success; ?></p>
                <?php endif; ?>
                <?php if ($booking_error): ?>
                    <p class="error"><?php echo $booking_error; ?></p>
                <?php endif; ?>
                <form method="POST" class="booking-form">
                    <input type="hidden" name="csrf_token" value="<?php echo generate_csrf_token(); ?>">
                    <div class="form-group">
                        <label for="booking_type">Booking Type</label>
                        <select id="booking_type" name="booking_type" required onchange="toggleTimeInputs()">
                            <option value="">Select Booking Type</option>
                            <option value="hourly">Hourly</option>
                            <option value="half-day">Half-Day</option>
                            <option value="full-day">Full-Day</option>
                            <option value="monthly">Monthly</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="booking_date">Booking Date</label>
                        <input type="date" id="booking_date" name="booking_date" required min="<?php echo date('Y-m-d'); ?>">
                    </div>
                    <div class="form-group time-inputs" style="display: none;">
                        <label for="start_time">Start Time</label>
                        <input type="time" id="start_time" name="start_time">
                    </div>
                    <div class="form-group time-inputs" style="display: none;">
                        <label for="end_time">End Time</label>
                        <input type="time" id="end_time" name="end_time">
                    </div>
                    <button type="submit" name="book" class="btn-primary">Confirm Booking</button>
                </form>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>OfficeLink</h4>
                    <p>Flexible workspaces for everyone.</p>
                </div>
                <div class="footer-section">
                    <h4>Links</h4>
                    <ul>
                        <li><a href="index.php#features">Features</a></li>
                        <li><a href="index.php#about">About</a></li>
                        <li><a href="signup.php">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +251 123 456 789</p>
                </div>
            </div>
            <p class="footer-bottom"> <?php echo date("Y"); ?> OfficeLink. All rights reserved.</p>
        </div>
    </footer>

    <script>
        function toggleMenu() {
            const menu = document.getElementById('nav-menu');
            const hamburger = document.querySelector('.hamburger');
            menu.classList.toggle('active');
            hamburger.classList.toggle('active');
        }

        function toggleTimeInputs() {
            const bookingType = document.getElementById('booking_type').value;
            const timeInputs = document.querySelectorAll('.time-inputs');
            if (bookingType === 'hourly') {
                timeInputs.forEach(input => input.style.display = 'block');
                timeInputs.forEach(input => input.querySelector('input').setAttribute('required', 'required'));
            } else {
                timeInputs.forEach(input => input.style.display = 'none');
                timeInputs.forEach(input => input.querySelector('input').removeAttribute('required'));
            }
        }
    </script>
</body>
</html>