<?php
require_once 'db_connect.php';
require_admin();

// Fetch all users (excluding admins)
$stmt = $pdo->query("SELECT * FROM users WHERE is_admin = 0 ORDER BY created_at DESC");
$users = $stmt->fetchAll(PDO::FETCH_ASSOC);

// Handle deleting a user
$delete_success = '';
$delete_error = '';
if (isset($_GET['delete']) && isset($_GET['csrf_token'])) {
    if (!verify_csrf_token($_GET['csrf_token'])) {
        $delete_error = "Invalid request. Please try again.";
    } else {
        $user_id = filter_var($_GET['delete'], FILTER_VALIDATE_INT);
        if ($user_id === false) {
            $delete_error = "Invalid user ID.";
        } else {
            try {
                $stmt = $pdo->prepare("DELETE FROM users WHERE id = ? AND is_admin = 0");
                $stmt->execute([$user_id]);
                $delete_success = "User deleted successfully!";
                header("Refresh: 2; url=admin_users.php");
            } catch (PDOException $e) {
                error_log("User deletion error: " . $e->getMessage());
                $delete_error = "Failed to delete user. Please try again.";
            }
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OfficeLink - Manage Users</title>
    <link rel="stylesheet" href="styles.css">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;600&display=swap" rel="stylesheet">
</head>
<body>
    <!-- Header -->
    <header>
        <div class="container">
            <div class="logo">OfficeLink Admin</div>
            <nav>
                <div class="hamburger" onclick="toggleMenu()">
                    <span></span>
                    <span></span>
                    <span></span>
                </div>
                <ul id="nav-menu">
                    <li><a href="admin_dashboard.php">Dashboard</a></li>
                    <li><a href="admin_workspaces.php">Manage Workspaces</a></li>
                    <li><a href="admin_bookings.php">Manage Bookings</a></li>
                    <li><a href="admin_users.php">Manage Users</a></li>
                    <li><a href="admin_dashboard.php?logout=true">Logout</a></li>
                </ul>
            </nav>
        </div>
    </header>

    <!-- Manage Users Section -->
    <section id="admin-users" class="admin-section">
        <div class="container">
            <h2>Manage Users</h2>
            <div class="admin-card">
                <h3>All Users</h3>
                <?php if ($delete_success): ?>
                    <p class="success"><?php echo $delete_success; ?></p>
                <?php endif; ?>
                <?php if ($delete_error): ?>
                    <p class="error"><?php echo $delete_error; ?></p>
                <?php endif; ?>
                <?php if (empty($users)): ?>
                    <p>No users found.</p>
                <?php else: ?>
                    <table class="admin-table">
                        <thead>
                            <tr>
                                <th>Name</th>
                                <th>Email</th>
                                <th>Phone Number</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users as $user): ?>
                                <tr>
                                    <td><?php echo htmlspecialchars($user['full_name']); ?></td>
                                    <td><?php echo htmlspecialchars($user['email']); ?></td>
                                    <td><?php echo htmlspecialchars($user['phone_number'] ?? 'N/A'); ?></td>
                                    <td>
                                        <a href="admin_users.php?delete=<?php echo $user['id']; ?>&csrf_token=<?php echo generate_csrf_token(); ?>" class="btn-delete" onclick="return confirm('Are you sure you want to delete this user?');">Delete</a>
                                    </td>
                                </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                <?php endif; ?>
            </div>
        </div>
    </section>

    <!-- Footer -->
    <footer>
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h4>OfficeLink</h4>
                    <p>Flexible workspaces for everyone.</p>
                </div>
                <div class="footer-section">
                    <h4>Links</h4>
                    <ul>
                        <li><a href="index.php#features">Features</a></li>
                        <li><a href="index.php#about">About</a></li>
                        <li><a href="signup.php">Sign Up</a></li>
                    </ul>
                </div>
                <div class="footer-section">
                    <h4>Contact</h4>
                    <p>Email: <EMAIL></p>
                    <p>Phone: +251 123 456 789</p>
                </div>
            </div>
            <p class="footer-bottom"> <?php echo date("Y"); ?> OfficeLink. All rights reserved.</p>
        </div>
    </footer>

    <script>
        function toggleMenu() {
            const menu = document.getElementById('nav-menu');
            const hamburger = document.querySelector('.hamburger');
            menu.classList.toggle('active');
            hamburger.classList.toggle('active');
        }
    </script>
</body>
</html>